'use client';

import React, { useState, useMemo } from 'react';
import { Plus, Search, Edit, Trash2, Phone, Mail, Eye } from 'lucide-react';
import { 
  useCustomers, 
  useCreateCustomer, 
  useUpdateCustomer, 
  useDeleteCustomer 
} from '@/hooks/useApi';
import { useAuth, withAuth } from '@/contexts/AuthContext';
import { Customer, CreateCustomerData } from '@/types';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Table from '@/components/ui/Table';
import Modal from '@/components/ui/Modal';
import CustomerForm from '@/components/forms/CustomerForm';
import { formatDate, debounce } from '@/lib/utils';
import { toast } from 'sonner';
import Link from 'next/link';

function CustomersPage() {
  const { hasPermission } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [deletingCustomer, setDeletingCustomer] = useState<Customer | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  const { data: customersData, isLoading } = useCustomers({
    search: searchTerm,
    page: currentPage,
    limit: pageSize,
  });

  const createCustomerMutation = useCreateCustomer();
  const updateCustomerMutation = useUpdateCustomer();
  const deleteCustomerMutation = useDeleteCustomer();

  const debouncedSearch = useMemo(
    () => debounce((value: string) => {
      setSearchTerm(value);
      setCurrentPage(1); // Reset to first page when searching
    }, 300),
    []
  );

  const canManageCustomers = hasPermission('manage_customers');

  const handleCreateCustomer = async (data: CreateCustomerData) => {
    try {
      await createCustomerMutation.mutateAsync(data);
      setIsCreateModalOpen(false);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleUpdateCustomer = async (data: CreateCustomerData) => {
    if (!editingCustomer) return;
    
    try {
      await updateCustomerMutation.mutateAsync({
        id: editingCustomer.id,
        data,
      });
      setEditingCustomer(null);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleDeleteCustomer = async () => {
    if (!deletingCustomer) return;

    try {
      await deleteCustomerMutation.mutateAsync(deletingCustomer.id);
      setDeletingCustomer(null);
      toast.success('Customer deleted successfully');
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const columns = [
    {
      key: 'name',
      header: 'Name',
      sortable: true,
    },
    {
      key: 'email',
      header: 'Email',
      render: (value: string) => (
        <div className="flex items-center">
          <Mail className="h-4 w-4 text-gray-400 mr-2" />
          <a href={`mailto:${value}`} className="text-blue-600 hover:underline">
            {value}
          </a>
        </div>
      ),
    },
    {
      key: 'phone',
      header: 'Phone',
      render: (value: string) => (
        <div className="flex items-center">
          <Phone className="h-4 w-4 text-gray-400 mr-2" />
          <a href={`tel:${value}`} className="text-blue-600 hover:underline">
            {value}
          </a>
        </div>
      ),
    },
    {
      key: 'address',
      header: 'Address',
      render: (value: string) => value || 'N/A',
    },
    {
      key: 'createdAt',
      header: 'Created',
      render: (value: string) => formatDate(value),
      sortable: true,
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_: any, customer: Customer) => (
        <div className="flex space-x-2">
          <Link href={`/dashboard/customers/${customer.id}`}>
            <Button variant="ghost" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
          {canManageCustomers && (
            <>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setEditingCustomer(customer)}
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setDeletingCustomer(customer)}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </>
          )}
        </div>
      ),
    },
  ];

  const customers = customersData?.data || [];
  const totalPages = customersData ? Math.ceil(customersData.total / pageSize) : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Customers</h1>
          <p className="text-gray-600">
            Manage your customer database
          </p>
        </div>
        {canManageCustomers && (
          <Button
            onClick={() => setIsCreateModalOpen(true)}
            leftIcon={<Plus className="h-4 w-4" />}
          >
            Add Customer
          </Button>
        )}
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <Input
              type="text"
              placeholder="Search customers by name, email, or phone..."
              leftIcon={<Search className="h-4 w-4" />}
              onChange={(e) => debouncedSearch(e.target.value)}
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <Table
          data={customers}
          columns={columns}
          isLoading={isLoading}
          emptyMessage="No customers found"
        />
        
        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, customersData?.total || 0)} of {customersData?.total || 0} results
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Create Modal */}
      {canManageCustomers && (
        <Modal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          title="Add New Customer"
          size="md"
        >
          <CustomerForm
            onSubmit={handleCreateCustomer}
            onCancel={() => setIsCreateModalOpen(false)}
            isLoading={createCustomerMutation.isPending}
          />
        </Modal>
      )}

      {/* Edit Modal */}
      {canManageCustomers && (
        <Modal
          isOpen={!!editingCustomer}
          onClose={() => setEditingCustomer(null)}
          title="Edit Customer"
          size="md"
        >
          {editingCustomer && (
            <CustomerForm
              customer={editingCustomer}
              onSubmit={handleUpdateCustomer}
              onCancel={() => setEditingCustomer(null)}
              isLoading={updateCustomerMutation.isPending}
            />
          )}
        </Modal>
      )}

      {/* Delete Confirmation Modal */}
      {canManageCustomers && (
        <Modal
          isOpen={!!deletingCustomer}
          onClose={() => setDeletingCustomer(null)}
          title="Delete Customer"
          size="sm"
        >
          {deletingCustomer && (
            <div className="space-y-4">
              <p className="text-gray-600">
                Are you sure you want to delete <strong>{deletingCustomer.name}</strong>? 
                This action cannot be undone.
              </p>
              <div className="flex justify-end space-x-3">
                <Button
                  variant="outline"
                  onClick={() => setDeletingCustomer(null)}
                  disabled={deleteCustomerMutation.isPending}
                >
                  Cancel
                </Button>
                <Button
                  variant="danger"
                  onClick={handleDeleteCustomer}
                  isLoading={deleteCustomerMutation.isPending}
                >
                  Delete Customer
                </Button>
              </div>
            </div>
          )}
        </Modal>
      )}
    </div>
  );
}

export default withAuth(CustomersPage);
