[{"/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/app/dashboard/customers/[id]/edit/page.tsx": "1", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/app/dashboard/customers/[id]/page.tsx": "2", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/app/dashboard/customers/new/page.tsx": "3", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/app/dashboard/customers/page.tsx": "4", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/app/layout.tsx": "5", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/app/login/page.tsx": "6", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/app/page.tsx": "7", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/app/profile/page.tsx": "8", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/dashboard/StatsCard.tsx": "9", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/forms/CustomerForm.tsx": "10", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/layout/Header.tsx": "11", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/layout/Layout.tsx": "12", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/layout/Sidebar.tsx": "13", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/ui/Badge.tsx": "14", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/ui/Button.tsx": "15", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/ui/Input.tsx": "16", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/ui/LoadingSpinner.tsx": "17", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/ui/Modal.tsx": "18", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/ui/Table.tsx": "19", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/contexts/AuthContext.tsx": "20", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/contexts/QueryProvider.tsx": "21", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/hooks/useApi.ts": "22", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/lib/utils.ts": "23", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/middleware.ts": "24", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/services/api.ts": "25", "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/types/index.ts": "26"}, {"size": 2616, "mtime": 1752353023187, "results": "27", "hashOfConfig": "28"}, {"size": 6904, "mtime": 1752353008659, "results": "29", "hashOfConfig": "28"}, {"size": 1736, "mtime": 1752353032733, "results": "30", "hashOfConfig": "28"}, {"size": 9411, "mtime": 1752352981323, "results": "31", "hashOfConfig": "28"}, {"size": 991, "mtime": 1752348057480, "results": "32", "hashOfConfig": "28"}, {"size": 4505, "mtime": 1752353118895, "results": "33", "hashOfConfig": "28"}, {"size": 776, "mtime": 1752353066045, "results": "34", "hashOfConfig": "28"}, {"size": 6672, "mtime": 1752353057189, "results": "35", "hashOfConfig": "28"}, {"size": 2066, "mtime": 1752348107972, "results": "36", "hashOfConfig": "28"}, {"size": 2908, "mtime": 1752348186138, "results": "37", "hashOfConfig": "28"}, {"size": 3639, "mtime": 1752353092830, "results": "38", "hashOfConfig": "28"}, {"size": 1151, "mtime": 1752348042643, "results": "39", "hashOfConfig": "28"}, {"size": 4386, "mtime": 1752352939144, "results": "40", "hashOfConfig": "28"}, {"size": 979, "mtime": 1752347993454, "results": "41", "hashOfConfig": "28"}, {"size": 2130, "mtime": 1752347940982, "results": "42", "hashOfConfig": "28"}, {"size": 1995, "mtime": 1752347951891, "results": "43", "hashOfConfig": "28"}, {"size": 765, "mtime": 1752348000367, "results": "44", "hashOfConfig": "28"}, {"size": 2827, "mtime": 1752347964336, "results": "45", "hashOfConfig": "28"}, {"size": 5174, "mtime": 1752347984817, "results": "46", "hashOfConfig": "28"}, {"size": 6004, "mtime": 1752352917447, "results": "47", "hashOfConfig": "28"}, {"size": 997, "mtime": 1752347884137, "results": "48", "hashOfConfig": "28"}, {"size": 11219, "mtime": 1752347921125, "results": "49", "hashOfConfig": "28"}, {"size": 5078, "mtime": 1752347806208, "results": "50", "hashOfConfig": "28"}, {"size": 838, "mtime": 1752352929627, "results": "51", "hashOfConfig": "28"}, {"size": 10221, "mtime": 1752352833143, "results": "52", "hashOfConfig": "28"}, {"size": 4519, "mtime": 1752352892921, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "ksago8", {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/app/dashboard/customers/[id]/edit/page.tsx", ["132", "133", "134"], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/app/dashboard/customers/[id]/page.tsx", ["135", "136", "137", "138", "139"], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/app/dashboard/customers/new/page.tsx", ["140"], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/app/dashboard/customers/page.tsx", ["141", "142", "143", "144"], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/app/layout.tsx", [], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/app/login/page.tsx", [], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/app/page.tsx", [], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/app/profile/page.tsx", ["145", "146"], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/dashboard/StatsCard.tsx", [], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/forms/CustomerForm.tsx", [], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/layout/Header.tsx", ["147"], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/layout/Sidebar.tsx", ["148", "149", "150", "151", "152", "153"], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/ui/Badge.tsx", [], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/ui/Button.tsx", [], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/ui/Input.tsx", [], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/ui/LoadingSpinner.tsx", [], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/ui/Modal.tsx", [], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/components/ui/Table.tsx", ["154", "155", "156"], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/contexts/AuthContext.tsx", ["157"], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/contexts/QueryProvider.tsx", ["158"], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/hooks/useApi.ts", ["159", "160", "161", "162", "163", "164", "165", "166"], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/lib/utils.ts", ["167", "168", "169", "170"], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/middleware.ts", [], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/services/api.ts", ["171", "172", "173"], [], "/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/types/index.ts", ["174"], [], {"ruleId": "175", "severity": 2, "message": "176", "line": 28, "column": 14, "nodeType": null, "messageId": "177", "endLine": 28, "endColumn": 19}, {"ruleId": "178", "severity": 2, "message": "179", "line": 49, "column": 59, "nodeType": "180", "messageId": "181", "suggestions": "182"}, {"ruleId": "178", "severity": 2, "message": "179", "line": 49, "column": 83, "nodeType": "180", "messageId": "181", "suggestions": "183"}, {"ruleId": "175", "severity": 2, "message": "184", "line": 3, "column": 17, "nodeType": null, "messageId": "177", "endLine": 3, "endColumn": 25}, {"ruleId": "175", "severity": 2, "message": "185", "line": 12, "column": 10, "nodeType": null, "messageId": "177", "endLine": 12, "endColumn": 16}, {"ruleId": "178", "severity": 2, "message": "179", "line": 40, "column": 59, "nodeType": "180", "messageId": "181", "suggestions": "186"}, {"ruleId": "178", "severity": 2, "message": "179", "line": 40, "column": 80, "nodeType": "180", "messageId": "181", "suggestions": "187"}, {"ruleId": "178", "severity": 2, "message": "179", "line": 185, "column": 36, "nodeType": "180", "messageId": "181", "suggestions": "188"}, {"ruleId": "175", "severity": 2, "message": "176", "line": 20, "column": 14, "nodeType": null, "messageId": "177", "endLine": 20, "endColumn": 19}, {"ruleId": "175", "severity": 2, "message": "176", "line": 55, "column": 14, "nodeType": null, "messageId": "177", "endLine": 55, "endColumn": 19}, {"ruleId": "175", "severity": 2, "message": "176", "line": 69, "column": 14, "nodeType": null, "messageId": "177", "endLine": 69, "endColumn": 19}, {"ruleId": "175", "severity": 2, "message": "176", "line": 81, "column": 14, "nodeType": null, "messageId": "177", "endLine": 81, "endColumn": 19}, {"ruleId": "189", "severity": 2, "message": "190", "line": 130, "column": 19, "nodeType": "191", "messageId": "192", "endLine": 130, "endColumn": 22, "suggestions": "193"}, {"ruleId": "175", "severity": 2, "message": "184", "line": 3, "column": 17, "nodeType": null, "messageId": "177", "endLine": 3, "endColumn": 25}, {"ruleId": "175", "severity": 2, "message": "194", "line": 7, "column": 22, "nodeType": null, "messageId": "177", "endLine": 7, "endColumn": 37}, {"ruleId": "175", "severity": 2, "message": "195", "line": 4, "column": 38, "nodeType": null, "messageId": "177", "endLine": 4, "endColumn": 46}, {"ruleId": "175", "severity": 2, "message": "196", "line": 8, "column": 3, "nodeType": null, "messageId": "177", "endLine": 8, "endColumn": 13}, {"ruleId": "175", "severity": 2, "message": "197", "line": 9, "column": 3, "nodeType": null, "messageId": "177", "endLine": 9, "endColumn": 16}, {"ruleId": "175", "severity": 2, "message": "198", "line": 10, "column": 3, "nodeType": null, "messageId": "177", "endLine": 10, "endColumn": 11}, {"ruleId": "175", "severity": 2, "message": "199", "line": 11, "column": 3, "nodeType": null, "messageId": "177", "endLine": 11, "endColumn": 9}, {"ruleId": "175", "severity": 2, "message": "200", "line": 12, "column": 3, "nodeType": null, "messageId": "177", "endLine": 12, "endColumn": 10}, {"ruleId": "175", "severity": 2, "message": "201", "line": 14, "column": 3, "nodeType": null, "messageId": "177", "endLine": 14, "endColumn": 18}, {"ruleId": "189", "severity": 2, "message": "190", "line": 7, "column": 20, "nodeType": "191", "messageId": "192", "endLine": 7, "endColumn": 23, "suggestions": "202"}, {"ruleId": "189", "severity": 2, "message": "190", "line": 23, "column": 41, "nodeType": "191", "messageId": "192", "endLine": 23, "endColumn": 44, "suggestions": "203"}, {"ruleId": "189", "severity": 2, "message": "190", "line": 41, "column": 53, "nodeType": "191", "messageId": "192", "endLine": 41, "endColumn": 56, "suggestions": "204"}, {"ruleId": "178", "severity": 2, "message": "179", "line": 226, "column": 49, "nodeType": "180", "messageId": "181", "suggestions": "205"}, {"ruleId": "189", "severity": 2, "message": "190", "line": 17, "column": 36, "nodeType": "191", "messageId": "192", "endLine": 17, "endColumn": 39, "suggestions": "206"}, {"ruleId": "175", "severity": 2, "message": "207", "line": 5, "column": 3, "nodeType": null, "messageId": "177", "endLine": 5, "endColumn": 11}, {"ruleId": "175", "severity": 2, "message": "185", "line": 7, "column": 3, "nodeType": null, "messageId": "177", "endLine": 7, "endColumn": 9}, {"ruleId": "175", "severity": 2, "message": "208", "line": 9, "column": 3, "nodeType": null, "messageId": "177", "endLine": 9, "endColumn": 17}, {"ruleId": "175", "severity": 2, "message": "209", "line": 12, "column": 3, "nodeType": null, "messageId": "177", "endLine": 12, "endColumn": 14}, {"ruleId": "175", "severity": 2, "message": "210", "line": 14, "column": 3, "nodeType": null, "messageId": "177", "endLine": 14, "endColumn": 10}, {"ruleId": "175", "severity": 2, "message": "211", "line": 16, "column": 3, "nodeType": null, "messageId": "177", "endLine": 16, "endColumn": 12}, {"ruleId": "175", "severity": 2, "message": "212", "line": 18, "column": 3, "nodeType": null, "messageId": "177", "endLine": 18, "endColumn": 17}, {"ruleId": "175", "severity": 2, "message": "213", "line": 19, "column": 3, "nodeType": null, "messageId": "177", "endLine": 19, "endColumn": 7}, {"ruleId": "189", "severity": 2, "message": "190", "line": 60, "column": 46, "nodeType": "191", "messageId": "192", "endLine": 60, "endColumn": 49, "suggestions": "214"}, {"ruleId": "189", "severity": 2, "message": "190", "line": 60, "column": 56, "nodeType": "191", "messageId": "192", "endLine": 60, "endColumn": 59, "suggestions": "215"}, {"ruleId": "189", "severity": 2, "message": "190", "line": 157, "column": 27, "nodeType": "191", "messageId": "192", "endLine": 157, "endColumn": 30, "suggestions": "216"}, {"ruleId": "189", "severity": 2, "message": "190", "line": 163, "column": 34, "nodeType": "191", "messageId": "192", "endLine": 163, "endColumn": 37, "suggestions": "217"}, {"ruleId": "175", "severity": 2, "message": "218", "line": 19, "column": 3, "nodeType": null, "messageId": "177", "endLine": 19, "endColumn": 14}, {"ruleId": "175", "severity": 2, "message": "219", "line": 21, "column": 3, "nodeType": null, "messageId": "177", "endLine": 21, "endColumn": 16}, {"ruleId": "175", "severity": 2, "message": "220", "line": 22, "column": 3, "nodeType": null, "messageId": "177", "endLine": 22, "endColumn": 14}, {"ruleId": "189", "severity": 2, "message": "190", "line": 236, "column": 28, "nodeType": "191", "messageId": "192", "endLine": 236, "endColumn": 31, "suggestions": "221"}, "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["222", "223", "224", "225"], ["226", "227", "228", "229"], "'useState' is defined but never used.", "'Device' is defined but never used.", ["230", "231", "232", "233"], ["234", "235", "236", "237"], ["238", "239", "240", "241"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["242", "243"], "'capitalizeFirst' is defined but never used.", "'Settings' is defined but never used.", "'Smartphone' is defined but never used.", "'ClipboardList' is defined but never used.", "'FileText' is defined but never used.", "'Wrench' is defined but never used.", "'Package' is defined but never used.", "'LayoutDashboard' is defined but never used.", ["244", "245"], ["246", "247"], ["248", "249"], ["250", "251", "252", "253"], ["254", "255"], "'Customer' is defined but never used.", "'ServiceRequest' is defined but never used.", "'ServiceNote' is defined but never used.", "'Invoice' is defined but never used.", "'StockItem' is defined but never used.", "'DashboardStats' is defined but never used.", "'User' is defined but never used.", ["256", "257"], ["258", "259"], ["260", "261"], ["262", "263"], "'ApiResponse' is defined but never used.", "'FilterOptions' is defined but never used.", "'SortOptions' is defined but never used.", ["264", "265"], {"messageId": "266", "data": "267", "fix": "268", "desc": "269"}, {"messageId": "266", "data": "270", "fix": "271", "desc": "272"}, {"messageId": "266", "data": "273", "fix": "274", "desc": "275"}, {"messageId": "266", "data": "276", "fix": "277", "desc": "278"}, {"messageId": "266", "data": "279", "fix": "280", "desc": "269"}, {"messageId": "266", "data": "281", "fix": "282", "desc": "272"}, {"messageId": "266", "data": "283", "fix": "284", "desc": "275"}, {"messageId": "266", "data": "285", "fix": "286", "desc": "278"}, {"messageId": "266", "data": "287", "fix": "288", "desc": "269"}, {"messageId": "266", "data": "289", "fix": "290", "desc": "272"}, {"messageId": "266", "data": "291", "fix": "292", "desc": "275"}, {"messageId": "266", "data": "293", "fix": "294", "desc": "278"}, {"messageId": "266", "data": "295", "fix": "296", "desc": "269"}, {"messageId": "266", "data": "297", "fix": "298", "desc": "272"}, {"messageId": "266", "data": "299", "fix": "300", "desc": "275"}, {"messageId": "266", "data": "301", "fix": "302", "desc": "278"}, {"messageId": "266", "data": "303", "fix": "304", "desc": "269"}, {"messageId": "266", "data": "305", "fix": "306", "desc": "272"}, {"messageId": "266", "data": "307", "fix": "308", "desc": "275"}, {"messageId": "266", "data": "309", "fix": "310", "desc": "278"}, {"messageId": "311", "fix": "312", "desc": "313"}, {"messageId": "314", "fix": "315", "desc": "316"}, {"messageId": "311", "fix": "317", "desc": "313"}, {"messageId": "314", "fix": "318", "desc": "316"}, {"messageId": "311", "fix": "319", "desc": "313"}, {"messageId": "314", "fix": "320", "desc": "316"}, {"messageId": "311", "fix": "321", "desc": "313"}, {"messageId": "314", "fix": "322", "desc": "316"}, {"messageId": "266", "data": "323", "fix": "324", "desc": "269"}, {"messageId": "266", "data": "325", "fix": "326", "desc": "272"}, {"messageId": "266", "data": "327", "fix": "328", "desc": "275"}, {"messageId": "266", "data": "329", "fix": "330", "desc": "278"}, {"messageId": "311", "fix": "331", "desc": "313"}, {"messageId": "314", "fix": "332", "desc": "316"}, {"messageId": "311", "fix": "333", "desc": "313"}, {"messageId": "314", "fix": "334", "desc": "316"}, {"messageId": "311", "fix": "335", "desc": "313"}, {"messageId": "314", "fix": "336", "desc": "316"}, {"messageId": "311", "fix": "337", "desc": "313"}, {"messageId": "314", "fix": "338", "desc": "316"}, {"messageId": "311", "fix": "339", "desc": "313"}, {"messageId": "314", "fix": "340", "desc": "316"}, {"messageId": "311", "fix": "341", "desc": "313"}, {"messageId": "314", "fix": "342", "desc": "316"}, "replaceWithAlt", {"alt": "343"}, {"range": "344", "text": "345"}, "Replace with `&apos;`.", {"alt": "346"}, {"range": "347", "text": "348"}, "Replace with `&lsquo;`.", {"alt": "349"}, {"range": "350", "text": "351"}, "Replace with `&#39;`.", {"alt": "352"}, {"range": "353", "text": "354"}, "Replace with `&rsquo;`.", {"alt": "343"}, {"range": "355", "text": "356"}, {"alt": "346"}, {"range": "357", "text": "358"}, {"alt": "349"}, {"range": "359", "text": "360"}, {"alt": "352"}, {"range": "361", "text": "362"}, {"alt": "343"}, {"range": "363", "text": "364"}, {"alt": "346"}, {"range": "365", "text": "366"}, {"alt": "349"}, {"range": "367", "text": "368"}, {"alt": "352"}, {"range": "369", "text": "370"}, {"alt": "343"}, {"range": "371", "text": "372"}, {"alt": "346"}, {"range": "373", "text": "374"}, {"alt": "349"}, {"range": "375", "text": "376"}, {"alt": "352"}, {"range": "377", "text": "378"}, {"alt": "343"}, {"range": "379", "text": "380"}, {"alt": "346"}, {"range": "381", "text": "382"}, {"alt": "349"}, {"range": "383", "text": "384"}, {"alt": "352"}, {"range": "385", "text": "386"}, "suggestUnknown", {"range": "387", "text": "388"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "389", "text": "390"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "391", "text": "388"}, {"range": "392", "text": "390"}, {"range": "393", "text": "388"}, {"range": "394", "text": "390"}, {"range": "395", "text": "388"}, {"range": "396", "text": "390"}, {"alt": "343"}, {"range": "397", "text": "398"}, {"alt": "346"}, {"range": "399", "text": "400"}, {"alt": "349"}, {"range": "401", "text": "402"}, {"alt": "352"}, {"range": "403", "text": "404"}, {"range": "405", "text": "388"}, {"range": "406", "text": "390"}, {"range": "407", "text": "388"}, {"range": "408", "text": "390"}, {"range": "409", "text": "388"}, {"range": "410", "text": "390"}, {"range": "411", "text": "388"}, {"range": "412", "text": "390"}, {"range": "413", "text": "388"}, {"range": "414", "text": "390"}, {"range": "415", "text": "388"}, {"range": "416", "text": "390"}, "&apos;", [1512, 1561], "The customer you&apos;re trying to edit doesn't exist.", "&lsquo;", [1512, 1561], "The customer you&lsquo;re trying to edit doesn't exist.", "&#39;", [1512, 1561], "The customer you&#39;re trying to edit doesn't exist.", "&rsquo;", [1512, 1561], "The customer you&rsquo;re trying to edit doesn't exist.", [1512, 1561], "The customer you're trying to edit doesn&apos;t exist.", [1512, 1561], "The customer you're trying to edit doesn&lsquo;t exist.", [1512, 1561], "The customer you're trying to edit doesn&#39;t exist.", [1512, 1561], "The customer you're trying to edit doesn&rsquo;t exist.", [1377, 1423], "The customer you&apos;re looking for doesn't exist.", [1377, 1423], "The customer you&lsquo;re looking for doesn't exist.", [1377, 1423], "The customer you&#39;re looking for doesn't exist.", [1377, 1423], "The customer you&rsquo;re looking for doesn't exist.", [1377, 1423], "The customer you're looking for doesn&apos;t exist.", [1377, 1423], "The customer you're looking for doesn&lsquo;t exist.", [1377, 1423], "The customer you're looking for doesn&#39;t exist.", [1377, 1423], "The customer you're looking for doesn&rsquo;t exist.", [6212, 6298], "\n                This customer doesn&apos;t have any devices registered yet.\n              ", [6212, 6298], "\n                This customer doesn&lsquo;t have any devices registered yet.\n              ", [6212, 6298], "\n                This customer doesn&#39;t have any devices registered yet.\n              ", [6212, 6298], "\n                This customer doesn&rsquo;t have any devices registered yet.\n              ", [3681, 3684], "unknown", [3681, 3684], "never", [146, 149], [146, 149], [517, 520], [517, 520], [926, 929], [926, 929], [5861, 5907], "You don&apos;t have permission to access this page.", [5861, 5907], "You don&lsquo;t have permission to access this page.", [5861, 5907], "You don&#39;t have permission to access this page.", [5861, 5907], "You don&rsquo;t have permission to access this page.", [486, 489], [486, 489], [1807, 1810], [1807, 1810], [1817, 1820], [1817, 1820], [4635, 4638], [4635, 4638], [4860, 4863], [4860, 4863], [4469, 4472], [4469, 4472]]