'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { User, Mail, Phone, MapPin } from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { CreateCustomerData, Customer } from '@/types';

const customerSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  phone: z.string().min(10, { message: 'Phone number must be at least 10 digits' }),
  address: z.string().optional(),
});

type CustomerFormData = z.infer<typeof customerSchema>;

interface CustomerFormProps {
  customer?: Customer;
  onSubmit: (data: CreateCustomerData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

const CustomerForm: React.FC<CustomerFormProps> = ({
  customer,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<CustomerFormData>({
    resolver: zodResolver(customerSchema),
    defaultValues: customer ? {
      name: customer.name,
      email: customer.email,
      phone: customer.phone,
      address: customer.address || '',
    } : undefined,
  });

  const handleFormSubmit = async (data: CustomerFormData) => {
    await onSubmit(data);
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
      <Input
        {...register('name')}
        label="Full Name"
        placeholder="Enter customer name"
        error={errors.name?.message}
        leftIcon={<User className="h-4 w-4" />}
        required
      />

      <Input
        {...register('email')}
        type="email"
        label="Email Address"
        placeholder="Enter email address"
        error={errors.email?.message}
        leftIcon={<Mail className="h-4 w-4" />}
        required
      />

      <Input
        {...register('phone')}
        type="tel"
        label="Phone Number"
        placeholder="Enter phone number"
        error={errors.phone?.message}
        leftIcon={<Phone className="h-4 w-4" />}
        required
      />

      <Input
        {...register('address')}
        label="Address"
        placeholder="Enter address (optional)"
        error={errors.address?.message}
        leftIcon={<MapPin className="h-4 w-4" />}
      />

      <div className="flex justify-end space-x-3 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          isLoading={isLoading}
          disabled={isLoading}
        >
          {customer ? 'Update Customer' : 'Create Customer'}
        </Button>
      </div>
    </form>
  );
};

export default CustomerForm;
