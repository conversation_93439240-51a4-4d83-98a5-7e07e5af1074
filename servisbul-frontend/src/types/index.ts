// User and Authentication Types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'technician' | 'viewer';
  tenantId: string;
  phone?: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

// Customer Types
export interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address?: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCustomerData {
  name: string;
  email: string;
  phone: string;
  address?: string;
}

// Device Types
export interface Device {
  id: string;
  customerId: string;
  customer?: Customer;
  brand: string;
  model: string;
  serialNumber?: string;
  warrantyExpiry?: string;
  notes?: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateDeviceData {
  customerId: string;
  brand: string;
  model: string;
  serialNumber?: string;
  warrantyExpiry?: string;
  notes?: string;
}

// Service Request Types
export type ServiceRequestStatus = 
  | 'Pending' 
  | 'Assigned' 
  | 'OnTheWay' 
  | 'InRepair' 
  | 'Completed' 
  | 'Cancelled';

export interface ServiceRequest {
  id: string;
  customerId: string;
  customer?: Customer;
  deviceId: string;
  device?: Device;
  technicianId?: string;
  technician?: User;
  title: string;
  description: string;
  status: ServiceRequestStatus;
  priority: 'Low' | 'Medium' | 'High' | 'Urgent';
  estimatedCost?: number;
  actualCost?: number;
  scheduledDate?: string;
  completedDate?: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  serviceNotes?: ServiceNote[];
}

export interface CreateServiceRequestData {
  customerId: string;
  deviceId: string;
  title: string;
  description: string;
  priority: 'Low' | 'Medium' | 'High' | 'Urgent';
  estimatedCost?: number;
  scheduledDate?: string;
}

export interface UpdateServiceRequestData {
  status?: ServiceRequestStatus;
  technicianId?: string;
  estimatedCost?: number;
  actualCost?: number;
  scheduledDate?: string;
  completedDate?: string;
}

// Service Note Types
export interface ServiceNote {
  id: string;
  serviceRequestId: string;
  userId: string;
  user?: User;
  note: string;
  imageUrl?: string;
  createdAt: string;
}

export interface CreateServiceNoteData {
  note: string;
  image?: File;
}

// Invoice Types
export interface Invoice {
  id: string;
  serviceRequestId: string;
  serviceRequest?: ServiceRequest;
  amount: number;
  isPaid: boolean;
  paidDate?: string;
  dueDate: string;
  invoiceNumber: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateInvoiceData {
  serviceRequestId: string;
  amount: number;
  dueDate: string;
}

// Stock Types
export interface StockItem {
  id: string;
  name: string;
  description?: string;
  sku: string;
  quantity: number;
  minThreshold: number;
  unitPrice: number;
  supplier?: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateStockItemData {
  name: string;
  description?: string;
  sku: string;
  quantity: number;
  minThreshold: number;
  unitPrice: number;
  supplier?: string;
}

// Dashboard Types
export interface DashboardStats {
  totalCustomers: number;
  totalRequests: number;
  assignedTechnicians: number;
  pendingRequests: number;
  completedRequests: number;
  totalRevenue: number;
  lowStockItems: number;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'select' | 'textarea';
  required?: boolean;
  options?: { value: string; label: string }[];
}

// Filter and Sort Types
export interface FilterOptions {
  search?: string;
  status?: string;
  customerId?: string;
  technicianId?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface SortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

// Tenant Types
export interface Tenant {
  id: string;
  name: string;
  logo?: string;
  domain: string;
  settings: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}
