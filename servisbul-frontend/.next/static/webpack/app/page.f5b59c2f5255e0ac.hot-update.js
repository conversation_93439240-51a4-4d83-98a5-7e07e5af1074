"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider,withAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isAuthenticated = !!user;\n    // Check if user has specific role(s)\n    const hasRole = (role)=>{\n        if (!user) return false;\n        if (Array.isArray(role)) {\n            return role.includes(user.role);\n        }\n        return user.role === role;\n    };\n    // Check if user has specific permission\n    const hasPermission = (permission)=>{\n        if (!user) return false;\n        // Define role-based permissions\n        const rolePermissions = {\n            admin: [\n                'view_dashboard',\n                'manage_customers',\n                'manage_devices',\n                'manage_requests',\n                'manage_invoices',\n                'manage_stock',\n                'manage_technicians',\n                'view_reports',\n                'manage_settings'\n            ],\n            technician: [\n                'view_customers',\n                'view_customer_details',\n                'view_devices',\n                'view_device_details'\n            ],\n            viewer: [\n                'view_customers',\n                'view_customer_details',\n                'view_devices',\n                'view_device_details'\n            ]\n        };\n        const userPermissions = rolePermissions[user.role] || [];\n        return userPermissions.includes(permission);\n    };\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async ()=>{\n                    const token = localStorage.getItem('auth-token');\n                    if (!token) {\n                        setIsLoading(false);\n                        return;\n                    }\n                    try {\n                        const userData = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getProfile();\n                        setUser(userData);\n                    } catch (error) {\n                        console.error('Failed to fetch user profile:', error);\n                        // Clear invalid token\n                        localStorage.removeItem('auth-token');\n                        localStorage.removeItem('refresh-token');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            initializeAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (credentials)=>{\n        try {\n            setIsLoading(true);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.login(credentials);\n            // Store tokens in localStorage\n            localStorage.setItem('auth-token', response.token);\n            if (response.refreshToken) {\n                localStorage.setItem('refresh-token', response.refreshToken);\n            }\n            setUser(response.user);\n            // Redirect based on user role\n            const redirectPath = getRedirectPath(response.user.role);\n            router.push(redirectPath);\n        } catch (error) {\n            console.error('Login failed:', error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.logout();\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            // Clear tokens and user state\n            localStorage.removeItem('auth-token');\n            localStorage.removeItem('refresh-token');\n            setUser(null);\n            router.push('/login');\n        }\n    };\n    const updateUser = (userData)=>{\n        if (user) {\n            setUser({\n                ...user,\n                ...userData\n            });\n        }\n    };\n    const getRedirectPath = (role)=>{\n        switch(role){\n            case 'admin':\n                return '/dashboard/customers';\n            case 'technician':\n                return '/dashboard/customers';\n            case 'viewer':\n                return '/dashboard/customers';\n            default:\n                return '/dashboard/customers';\n        }\n    };\n    const value = {\n        user,\n        isLoading,\n        isAuthenticated,\n        login,\n        logout,\n        updateUser,\n        hasRole,\n        hasPermission\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/contexts/AuthContext.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n_s1(AuthProvider, \"8WEfEbosx3NfLBPRVajZSQS3udc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\n// Higher-order component for protected routes\nfunction withAuth(Component, requiredRoles) {\n    var _s = $RefreshSig$();\n    return _s(function AuthenticatedComponent(props) {\n        _s();\n        const { isAuthenticated, isLoading, hasRole } = useAuth();\n        const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"withAuth.AuthenticatedComponent.useEffect\": ()=>{\n                if (!isLoading) {\n                    if (!isAuthenticated) {\n                        router.push('/login');\n                        return;\n                    }\n                    if (requiredRoles && !hasRole(requiredRoles)) {\n                        router.push('/unauthorized');\n                        return;\n                    }\n                }\n            }\n        }[\"withAuth.AuthenticatedComponent.useEffect\"], [\n            isAuthenticated,\n            isLoading,\n            hasRole,\n            router\n        ]);\n        if (isLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/contexts/AuthContext.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/contexts/AuthContext.tsx\",\n                lineNumber: 211,\n                columnNumber: 9\n            }, this);\n        }\n        if (!isAuthenticated) {\n            return null;\n        }\n        if (requiredRoles && !hasRole(requiredRoles)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: \"Access Denied\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/contexts/AuthContext.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"You don't have permission to access this page.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/contexts/AuthContext.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/contexts/AuthContext.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/contexts/AuthContext.tsx\",\n                lineNumber: 223,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/servisbul(frontend)/servisbul-frontend/src/contexts/AuthContext.tsx\",\n            lineNumber: 232,\n            columnNumber: 12\n        }, this);\n    }, \"LYGrx2kKfD7klNPVKa8A6mnFfGM=\", false, function() {\n        return [\n            useAuth,\n            next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n        ];\n    });\n}\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ })

});