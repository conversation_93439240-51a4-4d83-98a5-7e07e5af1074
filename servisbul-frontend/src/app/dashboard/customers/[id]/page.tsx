'use client';

import React, { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { ArrowLeft, Edit, Phone, Mail, MapPin, Smartphone, Plus } from 'lucide-react';
import { useCustomer, useDevices } from '@/hooks/useApi';
import { useAuth, withAuth } from '@/contexts/AuthContext';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import Table from '@/components/ui/Table';
import { formatDate } from '@/lib/utils';
import { Device } from '@/types';

function CustomerDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { hasPermission } = useAuth();
  const customerId = params.id as string;

  const { data: customer, isLoading: customerLoading } = useCustomer(customerId);
  const { data: devicesData, isLoading: devicesLoading } = useDevices({
    customerId,
    limit: 50,
  });

  const canManageCustomers = hasPermission('manage_customers');

  if (customerLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <LoadingSpinner size="lg" text="Loading customer details..." />
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Customer Not Found</h2>
        <p className="text-gray-600 mb-6">The customer you're looking for doesn't exist.</p>
        <Button onClick={() => router.push('/dashboard/customers')}>
          Back to Customers
        </Button>
      </div>
    );
  }

  const devices = devicesData?.data || [];

  const deviceColumns = [
    {
      key: 'brand',
      header: 'Brand',
    },
    {
      key: 'model',
      header: 'Model',
    },
    {
      key: 'serialNumber',
      header: 'Serial Number',
      render: (value: string) => value || 'N/A',
    },
    {
      key: 'warrantyExpiry',
      header: 'Warranty Expiry',
      render: (value: string) => value ? formatDate(value) : 'N/A',
    },
    {
      key: 'createdAt',
      header: 'Added',
      render: (value: string) => formatDate(value),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/dashboard/customers')}
            leftIcon={<ArrowLeft className="h-4 w-4" />}
          >
            Back to Customers
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{customer.name}</h1>
            <p className="text-gray-600">Customer Details</p>
          </div>
        </div>
        {canManageCustomers && (
          <Button
            onClick={() => router.push(`/dashboard/customers/${customer.id}/edit`)}
            leftIcon={<Edit className="h-4 w-4" />}
          >
            Edit Customer
          </Button>
        )}
      </div>

      {/* Customer Information */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Mail className="h-5 w-5 text-gray-400" />
              <div>
                <p className="text-sm font-medium text-gray-500">Email</p>
                <a
                  href={`mailto:${customer.email}`}
                  className="text-blue-600 hover:underline"
                >
                  {customer.email}
                </a>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <Phone className="h-5 w-5 text-gray-400" />
              <div>
                <p className="text-sm font-medium text-gray-500">Phone</p>
                <a
                  href={`tel:${customer.phone}`}
                  className="text-blue-600 hover:underline"
                >
                  {customer.phone}
                </a>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            {customer.address && (
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-gray-400 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-500">Address</p>
                  <p className="text-gray-900">{customer.address}</p>
                </div>
              </div>
            )}
            
            <div>
              <p className="text-sm font-medium text-gray-500">Customer Since</p>
              <p className="text-gray-900">{formatDate(customer.createdAt)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Devices Section */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Smartphone className="h-5 w-5 text-gray-400" />
              <h2 className="text-lg font-medium text-gray-900">Devices</h2>
            </div>
            {canManageCustomers && (
              <Button
                size="sm"
                onClick={() => router.push(`/dashboard/customers/${customer.id}/devices/new`)}
                leftIcon={<Plus className="h-4 w-4" />}
              >
                Add Device
              </Button>
            )}
          </div>
        </div>

        <div className="p-6">
          {devicesLoading ? (
            <div className="flex items-center justify-center py-8">
              <LoadingSpinner size="md" text="Loading devices..." />
            </div>
          ) : devices.length === 0 ? (
            <div className="text-center py-8">
              <Smartphone className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No devices found</h3>
              <p className="text-gray-600 mb-4">
                This customer doesn't have any devices registered yet.
              </p>
              {canManageCustomers && (
                <Button
                  onClick={() => router.push(`/dashboard/customers/${customer.id}/devices/new`)}
                  leftIcon={<Plus className="h-4 w-4" />}
                >
                  Add First Device
                </Button>
              )}
            </div>
          ) : (
            <Table
              data={devices}
              columns={deviceColumns}
              emptyMessage="No devices found"
            />
          )}
        </div>
      </div>
    </div>
  );
}

export default withAuth(CustomerDetailPage);
