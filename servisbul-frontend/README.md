# ServisBul Frontend

A modern and responsive frontend for a multitenant technical service management platform built with Next.js, TypeScript, and Tailwind CSS.

## Features

- **JWT-based Authentication** - Secure login with token storage in localStorage
- **Role-based Access Control** - Admin, Technician, and Viewer roles with different permissions
- **Customer Management** - Full CRUD operations for customer data
- **Responsive Design** - Mobile-friendly interface that works on all devices
- **Modern UI Components** - Built with Tailwind CSS and custom components
- **Data Fetching** - TanStack Query for efficient API calls and caching
- **Form Validation** - React Hook Form with Zod schema validation
- **Toast Notifications** - User feedback for actions and errors

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: TanStack Query
- **Forms**: React Hook Form + Zod
- **UI Components**: Custom components with Headless UI
- **Icons**: Lucide React
- **Notifications**: Sonner

## API Integration

The frontend connects to the ServisBul API at `https://servisbul-api.onrender.com/api` with the following endpoints:

- `POST /auth/login` - User authentication
- `GET /auth/profile` - Get user profile
- `GET /customers` - List customers with pagination and search
- `POST /customers` - Create new customer
- `GET /customers/:id` - Get customer details
- `PUT /customers/:id` - Update customer
- `DELETE /customers/:id` - Delete customer
- `GET /customers/:id/devices` - Get customer devices

## User Roles

### Admin
- Full access to all customer operations
- Can add, edit, and delete customers
- Access to all system features

### Technician
- Read-only access to customers
- Can view customer details and devices
- Cannot modify customer data

### Viewer
- Read-only access to customers
- Cannot add, edit, or delete customers

## Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd servisbul-frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```
   Update the API URL if needed.

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── dashboard/
│   │   └── customers/     # Customer management pages
│   ├── login/             # Login page
│   ├── profile/           # User profile page
│   └── layout.tsx         # Root layout
├── components/            # Reusable UI components
│   ├── forms/            # Form components
│   ├── layout/           # Layout components
│   └── ui/               # Basic UI components
├── contexts/             # React contexts
├── hooks/                # Custom hooks
├── lib/                  # Utility functions
├── services/             # API client
└── types/                # TypeScript type definitions
```

## Key Features

### Authentication Flow
1. User enters credentials on login page
2. JWT token is stored in localStorage
3. Token is sent in Authorization header for API requests
4. Protected routes redirect to login if no valid token

### Customer Management
- **List View**: Paginated table with search functionality
- **Detail View**: Complete customer information with devices
- **Add/Edit**: Form validation with error handling
- **Delete**: Confirmation modal for safety

### Responsive Design
- Mobile-first approach
- Collapsible sidebar navigation
- Touch-friendly interface
- Optimized for all screen sizes

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Code Quality

- TypeScript for type safety
- ESLint for code linting
- Tailwind CSS for consistent styling
- Component-based architecture

## Deployment

The application can be deployed to any platform that supports Next.js:

1. **Vercel** (recommended)
2. **Netlify**
3. **AWS Amplify**
4. **Docker**

Make sure to set the `NEXT_PUBLIC_API_URL` environment variable to your API endpoint.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
