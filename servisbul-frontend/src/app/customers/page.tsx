'use client';

import React, { useState, useMemo } from 'react';
import { Plus, Search, Edit, Trash2, Phone, Mail } from 'lucide-react';
import { 
  useCustomers, 
  useCreateCustomer, 
  useUpdateCustomer, 
  useDeleteCustomer 
} from '@/hooks/useApi';
import { withAuth } from '@/contexts/AuthContext';
import { Customer, CreateCustomerData } from '@/types';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Table from '@/components/ui/Table';
import Modal from '@/components/ui/Modal';
import CustomerForm from '@/components/forms/CustomerForm';
import { formatDate, debounce } from '@/lib/utils';
import { toast } from 'sonner';

function CustomersPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [deletingCustomer, setDeletingCustomer] = useState<Customer | null>(null);

  const { data: customersData, isLoading } = useCustomers({
    search: searchTerm,
    limit: 50,
  });

  const createCustomerMutation = useCreateCustomer();
  const updateCustomerMutation = useUpdateCustomer();
  const deleteCustomerMutation = useDeleteCustomer();

  const debouncedSearch = useMemo(
    () => debounce((value: string) => setSearchTerm(value), 300),
    []
  );

  const handleCreateCustomer = async (data: CreateCustomerData) => {
    try {
      await createCustomerMutation.mutateAsync(data);
      setIsCreateModalOpen(false);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleUpdateCustomer = async (data: CreateCustomerData) => {
    if (!editingCustomer) return;
    
    try {
      await updateCustomerMutation.mutateAsync({
        id: editingCustomer.id,
        data,
      });
      setEditingCustomer(null);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleDeleteCustomer = async () => {
    if (!deletingCustomer) return;

    try {
      await deleteCustomerMutation.mutateAsync(deletingCustomer.id);
      setDeletingCustomer(null);
      toast.success('Customer deleted successfully');
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const columns = [
    {
      key: 'name',
      header: 'Name',
      sortable: true,
    },
    {
      key: 'email',
      header: 'Email',
      render: (value: string) => (
        <div className="flex items-center">
          <Mail className="h-4 w-4 text-gray-400 mr-2" />
          <a href={`mailto:${value}`} className="text-blue-600 hover:underline">
            {value}
          </a>
        </div>
      ),
    },
    {
      key: 'phone',
      header: 'Phone',
      render: (value: string) => (
        <div className="flex items-center">
          <Phone className="h-4 w-4 text-gray-400 mr-2" />
          <a href={`tel:${value}`} className="text-blue-600 hover:underline">
            {value}
          </a>
        </div>
      ),
    },
    {
      key: 'createdAt',
      header: 'Created',
      render: (value: string) => formatDate(value),
      sortable: true,
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_: any, customer: Customer) => (
        <div className="flex space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setEditingCustomer(customer)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setDeletingCustomer(customer)}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  const customers = customersData?.data || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Customers</h1>
          <p className="text-gray-600">
            Manage your customer database
          </p>
        </div>
        <Button
          onClick={() => setIsCreateModalOpen(true)}
          leftIcon={<Plus className="h-4 w-4" />}
        >
          Add Customer
        </Button>
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg shadow p-4">
        <Input
          type="text"
          placeholder="Search customers..."
          leftIcon={<Search className="h-4 w-4" />}
          onChange={(e) => debouncedSearch(e.target.value)}
          className="max-w-md"
        />
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg shadow">
        <Table
          data={customers}
          columns={columns}
          isLoading={isLoading}
          emptyMessage="No customers found"
        />
      </div>

      {/* Create Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Add New Customer"
        size="md"
      >
        <CustomerForm
          onSubmit={handleCreateCustomer}
          onCancel={() => setIsCreateModalOpen(false)}
          isLoading={createCustomerMutation.isPending}
        />
      </Modal>

      {/* Edit Modal */}
      <Modal
        isOpen={!!editingCustomer}
        onClose={() => setEditingCustomer(null)}
        title="Edit Customer"
        size="md"
      >
        {editingCustomer && (
          <CustomerForm
            customer={editingCustomer}
            onSubmit={handleUpdateCustomer}
            onCancel={() => setEditingCustomer(null)}
            isLoading={updateCustomerMutation.isPending}
          />
        )}
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={!!deletingCustomer}
        onClose={() => setDeletingCustomer(null)}
        title="Delete Customer"
        size="sm"
      >
        {deletingCustomer && (
          <div className="space-y-4">
            <p className="text-gray-600">
              Are you sure you want to delete <strong>{deletingCustomer.name}</strong>? 
              This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setDeletingCustomer(null)}
                disabled={deleteCustomerMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                variant="danger"
                onClick={handleDeleteCustomer}
                isLoading={deleteCustomerMutation.isPending}
              >
                Delete Customer
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
}

export default withAuth(CustomersPage, ['admin', 'operator']);
