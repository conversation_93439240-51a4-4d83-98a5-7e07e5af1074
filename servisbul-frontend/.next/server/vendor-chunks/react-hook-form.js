"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-hook-form";
exports.ids = ["vendor-chunks/react-hook-form"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/react-hook-form/dist/index.esm.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   FormProvider: () => (/* binding */ FormProvider),\n/* harmony export */   appendErrors: () => (/* binding */ appendErrors),\n/* harmony export */   createFormControl: () => (/* binding */ createFormControl),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useFieldArray: () => (/* binding */ useFieldArray),\n/* harmony export */   useForm: () => (/* binding */ useForm),\n/* harmony export */   useFormContext: () => (/* binding */ useFormContext),\n/* harmony export */   useFormState: () => (/* binding */ useFormState),\n/* harmony export */   useWatch: () => (/* binding */ useWatch)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\nvar isCheckBoxInput = (element) => element.type === 'checkbox';\n\nvar isDateObject = (value) => value instanceof Date;\n\nvar isNullOrUndefined = (value) => value == null;\n\nconst isObjectType = (value) => typeof value === 'object';\nvar isObject = (value) => !isNullOrUndefined(value) &&\n    !Array.isArray(value) &&\n    isObjectType(value) &&\n    !isDateObject(value);\n\nvar getEventValue = (event) => isObject(event) && event.target\n    ? isCheckBoxInput(event.target)\n        ? event.target.checked\n        : event.target.value\n    : event;\n\nvar getNodeParentName = (name) => name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n\nvar isNameInFieldArray = (names, name) => names.has(getNodeParentName(name));\n\nvar isPlainObject = (tempObject) => {\n    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n    return (isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf'));\n};\n\nvar isWeb = typeof window !== 'undefined' &&\n    typeof window.HTMLElement !== 'undefined' &&\n    typeof document !== 'undefined';\n\nfunction cloneObject(data) {\n    let copy;\n    const isArray = Array.isArray(data);\n    const isFileListInstance = typeof FileList !== 'undefined' ? data instanceof FileList : false;\n    if (data instanceof Date) {\n        copy = new Date(data);\n    }\n    else if (!(isWeb && (data instanceof Blob || isFileListInstance)) &&\n        (isArray || isObject(data))) {\n        copy = isArray ? [] : {};\n        if (!isArray && !isPlainObject(data)) {\n            copy = data;\n        }\n        else {\n            for (const key in data) {\n                if (data.hasOwnProperty(key)) {\n                    copy[key] = cloneObject(data[key]);\n                }\n            }\n        }\n    }\n    else {\n        return data;\n    }\n    return copy;\n}\n\nvar isKey = (value) => /^\\w*$/.test(value);\n\nvar isUndefined = (val) => val === undefined;\n\nvar compact = (value) => Array.isArray(value) ? value.filter(Boolean) : [];\n\nvar stringToPath = (input) => compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n\nvar get = (object, path, defaultValue) => {\n    if (!path || !isObject(object)) {\n        return defaultValue;\n    }\n    const result = (isKey(path) ? [path] : stringToPath(path)).reduce((result, key) => isNullOrUndefined(result) ? result : result[key], object);\n    return isUndefined(result) || result === object\n        ? isUndefined(object[path])\n            ? defaultValue\n            : object[path]\n        : result;\n};\n\nvar isBoolean = (value) => typeof value === 'boolean';\n\nvar set = (object, path, value) => {\n    let index = -1;\n    const tempPath = isKey(path) ? [path] : stringToPath(path);\n    const length = tempPath.length;\n    const lastIndex = length - 1;\n    while (++index < length) {\n        const key = tempPath[index];\n        let newValue = value;\n        if (index !== lastIndex) {\n            const objValue = object[key];\n            newValue =\n                isObject(objValue) || Array.isArray(objValue)\n                    ? objValue\n                    : !isNaN(+tempPath[index + 1])\n                        ? []\n                        : {};\n        }\n        if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n            return;\n        }\n        object[key] = newValue;\n        object = object[key];\n    }\n};\n\nconst EVENTS = {\n    BLUR: 'blur',\n    FOCUS_OUT: 'focusout',\n    CHANGE: 'change',\n};\nconst VALIDATION_MODE = {\n    onBlur: 'onBlur',\n    onChange: 'onChange',\n    onSubmit: 'onSubmit',\n    onTouched: 'onTouched',\n    all: 'all',\n};\nconst INPUT_VALIDATION_RULES = {\n    max: 'max',\n    min: 'min',\n    maxLength: 'maxLength',\n    minLength: 'minLength',\n    pattern: 'pattern',\n    required: 'required',\n    validate: 'validate',\n};\n\nconst HookFormContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nHookFormContext.displayName = 'HookFormContext';\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst useFormContext = () => react__WEBPACK_IMPORTED_MODULE_0__.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst FormProvider = (props) => {\n    const { children, ...data } = props;\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(HookFormContext.Provider, { value: data }, children));\n};\n\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true) => {\n    const result = {\n        defaultValues: control._defaultValues,\n    };\n    for (const key in formState) {\n        Object.defineProperty(result, key, {\n            get: () => {\n                const _key = key;\n                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n                }\n                localProxyFormState && (localProxyFormState[_key] = true);\n                return formState[_key];\n            },\n        });\n    }\n    return result;\n};\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState(props) {\n    const methods = useFormContext();\n    const { control = methods.control, disabled, name, exact } = props || {};\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._formState);\n    const _localProxyFormState = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        isDirty: false,\n        isLoading: false,\n        dirtyFields: false,\n        touchedFields: false,\n        validatingFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false,\n    });\n    useIsomorphicLayoutEffect(() => control._subscribe({\n        name,\n        formState: _localProxyFormState.current,\n        exact,\n        callback: (formState) => {\n            !disabled &&\n                updateFormState({\n                    ...control._formState,\n                    ...formState,\n                });\n        },\n    }), [name, disabled, exact]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        _localProxyFormState.current.isValid && control._setValid(true);\n    }, [control]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => getProxyFormState(formState, control, _localProxyFormState.current, false), [formState, control]);\n}\n\nvar isString = (value) => typeof value === 'string';\n\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue) => {\n    if (isString(names)) {\n        isGlobal && _names.watch.add(names);\n        return get(formValues, names, defaultValue);\n    }\n    if (Array.isArray(names)) {\n        return names.map((fieldName) => (isGlobal && _names.watch.add(fieldName),\n            get(formValues, fieldName)));\n    }\n    isGlobal && (_names.watchAll = true);\n    return formValues;\n};\n\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nfunction useWatch(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, defaultValue, disabled, exact, } = props || {};\n    const _defaultValue = react__WEBPACK_IMPORTED_MODULE_0__.useRef(defaultValue);\n    const [value, updateValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getWatch(name, _defaultValue.current));\n    useIsomorphicLayoutEffect(() => control._subscribe({\n        name,\n        formState: {\n            values: true,\n        },\n        exact,\n        callback: (formState) => !disabled &&\n            updateValue(generateWatchOutput(name, control._names, formState.values || control._formValues, false, _defaultValue.current)),\n    }), [name, control, disabled, exact]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => control._removeUnmounted());\n    return value;\n}\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nfunction useController(props) {\n    const methods = useFormContext();\n    const { name, disabled, control = methods.control, shouldUnregister } = props;\n    const isArrayField = isNameInFieldArray(control._names.array, name);\n    const value = useWatch({\n        control,\n        name,\n        defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n        exact: true,\n    });\n    const formState = useFormState({\n        control,\n        name,\n        exact: true,\n    });\n    const _props = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n    const _registerProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control.register(name, {\n        ...props.rules,\n        value,\n        ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }));\n    const fieldState = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => Object.defineProperties({}, {\n        invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n        },\n        isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n        },\n        isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n        },\n        isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n        },\n        error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n        },\n    }), [formState, name]);\n    const onChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event) => _registerProps.current.onChange({\n        target: {\n            value: getEventValue(event),\n            name: name,\n        },\n        type: EVENTS.CHANGE,\n    }), [name]);\n    const onBlur = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => _registerProps.current.onBlur({\n        target: {\n            value: get(control._formValues, name),\n            name: name,\n        },\n        type: EVENTS.BLUR,\n    }), [name, control._formValues]);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((elm) => {\n        const field = get(control._fields, name);\n        if (field && elm) {\n            field._f.ref = {\n                focus: () => elm.focus && elm.focus(),\n                select: () => elm.select && elm.select(),\n                setCustomValidity: (message) => elm.setCustomValidity(message),\n                reportValidity: () => elm.reportValidity(),\n            };\n        }\n    }, [control._fields, name]);\n    const field = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n        name,\n        value,\n        ...(isBoolean(disabled) || formState.disabled\n            ? { disabled: formState.disabled || disabled }\n            : {}),\n        onChange,\n        onBlur,\n        ref,\n    }), [name, disabled, formState.disabled, onChange, onBlur, ref, value]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n        control.register(name, {\n            ..._props.current.rules,\n            ...(isBoolean(_props.current.disabled)\n                ? { disabled: _props.current.disabled }\n                : {}),\n        });\n        const updateMounted = (name, value) => {\n            const field = get(control._fields, name);\n            if (field && field._f) {\n                field._f.mount = value;\n            }\n        };\n        updateMounted(name, true);\n        if (_shouldUnregisterField) {\n            const value = cloneObject(get(control._options.defaultValues, name));\n            set(control._defaultValues, name, value);\n            if (isUndefined(get(control._formValues, name))) {\n                set(control._formValues, name, value);\n            }\n        }\n        !isArrayField && control.register(name);\n        return () => {\n            (isArrayField\n                ? _shouldUnregisterField && !control._state.action\n                : _shouldUnregisterField)\n                ? control.unregister(name)\n                : updateMounted(name, false);\n        };\n    }, [name, control, isArrayField, shouldUnregister]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        control._setDisabledField({\n            disabled,\n            name,\n        });\n    }, [disabled, name, control]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n        field,\n        formState,\n        fieldState,\n    }), [field, formState, fieldState]);\n}\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = (props) => props.render(useController(props));\n\nconst flatten = (obj) => {\n    const output = {};\n    for (const key of Object.keys(obj)) {\n        if (isObjectType(obj[key]) && obj[key] !== null) {\n            const nested = flatten(obj[key]);\n            for (const nestedKey of Object.keys(nested)) {\n                output[`${key}.${nestedKey}`] = nested[nestedKey];\n            }\n        }\n        else {\n            output[key] = obj[key];\n        }\n    }\n    return output;\n};\n\nconst POST_REQUEST = 'post';\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form(props) {\n    const methods = useFormContext();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;\n    const submit = async (event) => {\n        let hasError = false;\n        let type = '';\n        await control.handleSubmit(async (data) => {\n            const formData = new FormData();\n            let formDataJson = '';\n            try {\n                formDataJson = JSON.stringify(data);\n            }\n            catch (_a) { }\n            const flattenFormValues = flatten(control._formValues);\n            for (const key in flattenFormValues) {\n                formData.append(key, flattenFormValues[key]);\n            }\n            if (onSubmit) {\n                await onSubmit({\n                    data,\n                    event,\n                    method,\n                    formData,\n                    formDataJson,\n                });\n            }\n            if (action) {\n                try {\n                    const shouldStringifySubmissionData = [\n                        headers && headers['Content-Type'],\n                        encType,\n                    ].some((value) => value && value.includes('json'));\n                    const response = await fetch(String(action), {\n                        method,\n                        headers: {\n                            ...headers,\n                            ...(encType ? { 'Content-Type': encType } : {}),\n                        },\n                        body: shouldStringifySubmissionData ? formDataJson : formData,\n                    });\n                    if (response &&\n                        (validateStatus\n                            ? !validateStatus(response.status)\n                            : response.status < 200 || response.status >= 300)) {\n                        hasError = true;\n                        onError && onError({ response });\n                        type = String(response.status);\n                    }\n                    else {\n                        onSuccess && onSuccess({ response });\n                    }\n                }\n                catch (error) {\n                    hasError = true;\n                    onError && onError({ error });\n                }\n            }\n        })(event);\n        if (hasError && props.control) {\n            props.control._subjects.state.next({\n                isSubmitSuccessful: false,\n            });\n            props.control.setError('root.server', {\n                type,\n            });\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        setMounted(true);\n    }, []);\n    return render ? (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render({\n        submit,\n    }))) : (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"form\", { noValidate: mounted, action: action, method: method, encType: encType, onSubmit: submit, ...rest }, children));\n}\n\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message) => validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n            ...(errors[name] && errors[name].types ? errors[name].types : {}),\n            [type]: message || true,\n        },\n    }\n    : {};\n\nvar convertToArrayPayload = (value) => (Array.isArray(value) ? value : [value]);\n\nvar createSubject = () => {\n    let _observers = [];\n    const next = (value) => {\n        for (const observer of _observers) {\n            observer.next && observer.next(value);\n        }\n    };\n    const subscribe = (observer) => {\n        _observers.push(observer);\n        return {\n            unsubscribe: () => {\n                _observers = _observers.filter((o) => o !== observer);\n            },\n        };\n    };\n    const unsubscribe = () => {\n        _observers = [];\n    };\n    return {\n        get observers() {\n            return _observers;\n        },\n        next,\n        subscribe,\n        unsubscribe,\n    };\n};\n\nvar isPrimitive = (value) => isNullOrUndefined(value) || !isObjectType(value);\n\nfunction deepEqual(object1, object2, _internal_visited = new WeakSet()) {\n    if (isPrimitive(object1) || isPrimitive(object2)) {\n        return object1 === object2;\n    }\n    if (isDateObject(object1) && isDateObject(object2)) {\n        return object1.getTime() === object2.getTime();\n    }\n    const keys1 = Object.keys(object1);\n    const keys2 = Object.keys(object2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    if (_internal_visited.has(object1) || _internal_visited.has(object2)) {\n        return true;\n    }\n    _internal_visited.add(object1);\n    _internal_visited.add(object2);\n    for (const key of keys1) {\n        const val1 = object1[key];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (key !== 'ref') {\n            const val2 = object2[key];\n            if ((isDateObject(val1) && isDateObject(val2)) ||\n                (isObject(val1) && isObject(val2)) ||\n                (Array.isArray(val1) && Array.isArray(val2))\n                ? !deepEqual(val1, val2, _internal_visited)\n                : val1 !== val2) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\n\nvar isEmptyObject = (value) => isObject(value) && !Object.keys(value).length;\n\nvar isFileInput = (element) => element.type === 'file';\n\nvar isFunction = (value) => typeof value === 'function';\n\nvar isHTMLElement = (value) => {\n    if (!isWeb) {\n        return false;\n    }\n    const owner = value ? value.ownerDocument : 0;\n    return (value instanceof\n        (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement));\n};\n\nvar isMultipleSelect = (element) => element.type === `select-multiple`;\n\nvar isRadioInput = (element) => element.type === 'radio';\n\nvar isRadioOrCheckbox = (ref) => isRadioInput(ref) || isCheckBoxInput(ref);\n\nvar live = (ref) => isHTMLElement(ref) && ref.isConnected;\n\nfunction baseGet(object, updatePath) {\n    const length = updatePath.slice(0, -1).length;\n    let index = 0;\n    while (index < length) {\n        object = isUndefined(object) ? index++ : object[updatePath[index++]];\n    }\n    return object;\n}\nfunction isEmptyArray(obj) {\n    for (const key in obj) {\n        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction unset(object, path) {\n    const paths = Array.isArray(path)\n        ? path\n        : isKey(path)\n            ? [path]\n            : stringToPath(path);\n    const childObject = paths.length === 1 ? object : baseGet(object, paths);\n    const index = paths.length - 1;\n    const key = paths[index];\n    if (childObject) {\n        delete childObject[key];\n    }\n    if (index !== 0 &&\n        ((isObject(childObject) && isEmptyObject(childObject)) ||\n            (Array.isArray(childObject) && isEmptyArray(childObject)))) {\n        unset(object, paths.slice(0, -1));\n    }\n    return object;\n}\n\nvar objectHasFunction = (data) => {\n    for (const key in data) {\n        if (isFunction(data[key])) {\n            return true;\n        }\n    }\n    return false;\n};\n\nfunction markFieldsDirty(data, fields = {}) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for (const key in data) {\n            if (Array.isArray(data[key]) ||\n                (isObject(data[key]) && !objectHasFunction(data[key]))) {\n                fields[key] = Array.isArray(data[key]) ? [] : {};\n                markFieldsDirty(data[key], fields[key]);\n            }\n            else if (!isNullOrUndefined(data[key])) {\n                fields[key] = true;\n            }\n        }\n    }\n    return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for (const key in data) {\n            if (Array.isArray(data[key]) ||\n                (isObject(data[key]) && !objectHasFunction(data[key]))) {\n                if (isUndefined(formValues) ||\n                    isPrimitive(dirtyFieldsFromValues[key])) {\n                    dirtyFieldsFromValues[key] = Array.isArray(data[key])\n                        ? markFieldsDirty(data[key], [])\n                        : { ...markFieldsDirty(data[key]) };\n                }\n                else {\n                    getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n                }\n            }\n            else {\n                dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n            }\n        }\n    }\n    return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues) => getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\n\nconst defaultResult = {\n    value: false,\n    isValid: false,\n};\nconst validResult = { value: true, isValid: true };\nvar getCheckboxValue = (options) => {\n    if (Array.isArray(options)) {\n        if (options.length > 1) {\n            const values = options\n                .filter((option) => option && option.checked && !option.disabled)\n                .map((option) => option.value);\n            return { value: values, isValid: !!values.length };\n        }\n        return options[0].checked && !options[0].disabled\n            ? // @ts-expect-error expected to work in the browser\n                options[0].attributes && !isUndefined(options[0].attributes.value)\n                    ? isUndefined(options[0].value) || options[0].value === ''\n                        ? validResult\n                        : { value: options[0].value, isValid: true }\n                    : validResult\n            : defaultResult;\n    }\n    return defaultResult;\n};\n\nvar getFieldValueAs = (value, { valueAsNumber, valueAsDate, setValueAs }) => isUndefined(value)\n    ? value\n    : valueAsNumber\n        ? value === ''\n            ? NaN\n            : value\n                ? +value\n                : value\n        : valueAsDate && isString(value)\n            ? new Date(value)\n            : setValueAs\n                ? setValueAs(value)\n                : value;\n\nconst defaultReturn = {\n    isValid: false,\n    value: null,\n};\nvar getRadioValue = (options) => Array.isArray(options)\n    ? options.reduce((previous, option) => option && option.checked && !option.disabled\n        ? {\n            isValid: true,\n            value: option.value,\n        }\n        : previous, defaultReturn)\n    : defaultReturn;\n\nfunction getFieldValue(_f) {\n    const ref = _f.ref;\n    if (isFileInput(ref)) {\n        return ref.files;\n    }\n    if (isRadioInput(ref)) {\n        return getRadioValue(_f.refs).value;\n    }\n    if (isMultipleSelect(ref)) {\n        return [...ref.selectedOptions].map(({ value }) => value);\n    }\n    if (isCheckBoxInput(ref)) {\n        return getCheckboxValue(_f.refs).value;\n    }\n    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation) => {\n    const fields = {};\n    for (const name of fieldsNames) {\n        const field = get(_fields, name);\n        field && set(fields, name, field._f);\n    }\n    return {\n        criteriaMode,\n        names: [...fieldsNames],\n        fields,\n        shouldUseNativeValidation,\n    };\n};\n\nvar isRegex = (value) => value instanceof RegExp;\n\nvar getRuleValue = (rule) => isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n        ? rule.source\n        : isObject(rule)\n            ? isRegex(rule.value)\n                ? rule.value.source\n                : rule.value\n            : rule;\n\nvar getValidationModes = (mode) => ({\n    isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n    isOnBlur: mode === VALIDATION_MODE.onBlur,\n    isOnChange: mode === VALIDATION_MODE.onChange,\n    isOnAll: mode === VALIDATION_MODE.all,\n    isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\nvar hasPromiseValidation = (fieldReference) => !!fieldReference &&\n    !!fieldReference.validate &&\n    !!((isFunction(fieldReference.validate) &&\n        fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n        (isObject(fieldReference.validate) &&\n            Object.values(fieldReference.validate).find((validateFunction) => validateFunction.constructor.name === ASYNC_FUNCTION)));\n\nvar hasValidation = (options) => options.mount &&\n    (options.required ||\n        options.min ||\n        options.max ||\n        options.maxLength ||\n        options.minLength ||\n        options.pattern ||\n        options.validate);\n\nvar isWatched = (name, _names, isBlurEvent) => !isBlurEvent &&\n    (_names.watchAll ||\n        _names.watch.has(name) ||\n        [..._names.watch].some((watchName) => name.startsWith(watchName) &&\n            /^\\.\\w+/.test(name.slice(watchName.length))));\n\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly) => {\n    for (const key of fieldsNames || Object.keys(fields)) {\n        const field = get(fields, key);\n        if (field) {\n            const { _f, ...currentField } = field;\n            if (_f) {\n                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n                    return true;\n                }\n                else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n                    return true;\n                }\n                else {\n                    if (iterateFieldsByAction(currentField, action)) {\n                        break;\n                    }\n                }\n            }\n            else if (isObject(currentField)) {\n                if (iterateFieldsByAction(currentField, action)) {\n                    break;\n                }\n            }\n        }\n    }\n    return;\n};\n\nfunction schemaErrorLookup(errors, _fields, name) {\n    const error = get(errors, name);\n    if (error || isKey(name)) {\n        return {\n            error,\n            name,\n        };\n    }\n    const names = name.split('.');\n    while (names.length) {\n        const fieldName = names.join('.');\n        const field = get(_fields, fieldName);\n        const foundError = get(errors, fieldName);\n        if (field && !Array.isArray(field) && name !== fieldName) {\n            return { name };\n        }\n        if (foundError && foundError.type) {\n            return {\n                name: fieldName,\n                error: foundError,\n            };\n        }\n        if (foundError && foundError.root && foundError.root.type) {\n            return {\n                name: `${fieldName}.root`,\n                error: foundError.root,\n            };\n        }\n        names.pop();\n    }\n    return {\n        name,\n    };\n}\n\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot) => {\n    updateFormState(formStateData);\n    const { name, ...formState } = formStateData;\n    return (isEmptyObject(formState) ||\n        Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n        Object.keys(formState).find((key) => _proxyFormState[key] ===\n            (!isRoot || VALIDATION_MODE.all)));\n};\n\nvar shouldSubscribeByName = (name, signalName, exact) => !name ||\n    !signalName ||\n    name === signalName ||\n    convertToArrayPayload(name).some((currentName) => currentName &&\n        (exact\n            ? currentName === signalName\n            : currentName.startsWith(signalName) ||\n                signalName.startsWith(currentName)));\n\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode) => {\n    if (mode.isOnAll) {\n        return false;\n    }\n    else if (!isSubmitted && mode.isOnTouch) {\n        return !(isTouched || isBlurEvent);\n    }\n    else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n        return !isBlurEvent;\n    }\n    else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n        return isBlurEvent;\n    }\n    return true;\n};\n\nvar unsetEmptyArray = (ref, name) => !compact(get(ref, name)).length && unset(ref, name);\n\nvar updateFieldArrayRootError = (errors, error, name) => {\n    const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n    set(fieldArrayErrors, 'root', error[name]);\n    set(errors, name, fieldArrayErrors);\n    return errors;\n};\n\nvar isMessage = (value) => isString(value);\n\nfunction getValidateError(result, ref, type = 'validate') {\n    if (isMessage(result) ||\n        (Array.isArray(result) && result.every(isMessage)) ||\n        (isBoolean(result) && !result)) {\n        return {\n            type,\n            message: isMessage(result) ? result : '',\n            ref,\n        };\n    }\n}\n\nvar getValueAndMessage = (validationData) => isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n    };\n\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray) => {\n    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount, } = field._f;\n    const inputValue = get(formValues, name);\n    if (!mount || disabledFieldNames.has(name)) {\n        return {};\n    }\n    const inputRef = refs ? refs[0] : ref;\n    const setCustomValidity = (message) => {\n        if (shouldUseNativeValidation && inputRef.reportValidity) {\n            inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n            inputRef.reportValidity();\n        }\n    };\n    const error = {};\n    const isRadio = isRadioInput(ref);\n    const isCheckBox = isCheckBoxInput(ref);\n    const isRadioOrCheckbox = isRadio || isCheckBox;\n    const isEmpty = ((valueAsNumber || isFileInput(ref)) &&\n        isUndefined(ref.value) &&\n        isUndefined(inputValue)) ||\n        (isHTMLElement(ref) && ref.value === '') ||\n        inputValue === '' ||\n        (Array.isArray(inputValue) && !inputValue.length);\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength) => {\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\n        error[name] = {\n            type: exceedMax ? maxType : minType,\n            message,\n            ref,\n            ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n        };\n    };\n    if (isFieldArray\n        ? !Array.isArray(inputValue) || !inputValue.length\n        : required &&\n            ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n                (isBoolean(inputValue) && !inputValue) ||\n                (isCheckBox && !getCheckboxValue(refs).isValid) ||\n                (isRadio && !getRadioValue(refs).isValid))) {\n        const { value, message } = isMessage(required)\n            ? { value: !!required, message: required }\n            : getValueAndMessage(required);\n        if (value) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.required,\n                message,\n                ref: inputRef,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n        let exceedMax;\n        let exceedMin;\n        const maxOutput = getValueAndMessage(max);\n        const minOutput = getValueAndMessage(min);\n        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n            const valueNumber = ref.valueAsNumber ||\n                (inputValue ? +inputValue : inputValue);\n            if (!isNullOrUndefined(maxOutput.value)) {\n                exceedMax = valueNumber > maxOutput.value;\n            }\n            if (!isNullOrUndefined(minOutput.value)) {\n                exceedMin = valueNumber < minOutput.value;\n            }\n        }\n        else {\n            const valueDate = ref.valueAsDate || new Date(inputValue);\n            const convertTimeToDate = (time) => new Date(new Date().toDateString() + ' ' + time);\n            const isTime = ref.type == 'time';\n            const isWeek = ref.type == 'week';\n            if (isString(maxOutput.value) && inputValue) {\n                exceedMax = isTime\n                    ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n                    : isWeek\n                        ? inputValue > maxOutput.value\n                        : valueDate > new Date(maxOutput.value);\n            }\n            if (isString(minOutput.value) && inputValue) {\n                exceedMin = isTime\n                    ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n                    : isWeek\n                        ? inputValue < minOutput.value\n                        : valueDate < new Date(minOutput.value);\n            }\n        }\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if ((maxLength || minLength) &&\n        !isEmpty &&\n        (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))) {\n        const maxLengthOutput = getValueAndMessage(maxLength);\n        const minLengthOutput = getValueAndMessage(minLength);\n        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) &&\n            inputValue.length > +maxLengthOutput.value;\n        const exceedMin = !isNullOrUndefined(minLengthOutput.value) &&\n            inputValue.length < +minLengthOutput.value;\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if (pattern && !isEmpty && isString(inputValue)) {\n        const { value: patternValue, message } = getValueAndMessage(pattern);\n        if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.pattern,\n                message,\n                ref,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (validate) {\n        if (isFunction(validate)) {\n            const result = await validate(inputValue, formValues);\n            const validateError = getValidateError(result, inputRef);\n            if (validateError) {\n                error[name] = {\n                    ...validateError,\n                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message),\n                };\n                if (!validateAllFieldCriteria) {\n                    setCustomValidity(validateError.message);\n                    return error;\n                }\n            }\n        }\n        else if (isObject(validate)) {\n            let validationResult = {};\n            for (const key in validate) {\n                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n                    break;\n                }\n                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n                if (validateError) {\n                    validationResult = {\n                        ...validateError,\n                        ...appendErrorsCurry(key, validateError.message),\n                    };\n                    setCustomValidity(validateError.message);\n                    if (validateAllFieldCriteria) {\n                        error[name] = validationResult;\n                    }\n                }\n            }\n            if (!isEmptyObject(validationResult)) {\n                error[name] = {\n                    ref: inputRef,\n                    ...validationResult,\n                };\n                if (!validateAllFieldCriteria) {\n                    return error;\n                }\n            }\n        }\n    }\n    setCustomValidity(true);\n    return error;\n};\n\nconst defaultOptions = {\n    mode: VALIDATION_MODE.onSubmit,\n    reValidateMode: VALIDATION_MODE.onChange,\n    shouldFocusError: true,\n};\nfunction createFormControl(props = {}) {\n    let _options = {\n        ...defaultOptions,\n        ...props,\n    };\n    let _formState = {\n        submitCount: 0,\n        isDirty: false,\n        isReady: false,\n        isLoading: isFunction(_options.defaultValues),\n        isValidating: false,\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        touchedFields: {},\n        dirtyFields: {},\n        validatingFields: {},\n        errors: _options.errors || {},\n        disabled: _options.disabled || false,\n    };\n    let _fields = {};\n    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values)\n        ? cloneObject(_options.defaultValues || _options.values) || {}\n        : {};\n    let _formValues = _options.shouldUnregister\n        ? {}\n        : cloneObject(_defaultValues);\n    let _state = {\n        action: false,\n        mount: false,\n        watch: false,\n    };\n    let _names = {\n        mount: new Set(),\n        disabled: new Set(),\n        unMount: new Set(),\n        array: new Set(),\n        watch: new Set(),\n    };\n    let delayErrorCallback;\n    let timer = 0;\n    const _proxyFormState = {\n        isDirty: false,\n        dirtyFields: false,\n        validatingFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false,\n    };\n    let _proxySubscribeFormState = {\n        ..._proxyFormState,\n    };\n    const _subjects = {\n        array: createSubject(),\n        state: createSubject(),\n    };\n    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n    const debounce = (callback) => (wait) => {\n        clearTimeout(timer);\n        timer = setTimeout(callback, wait);\n    };\n    const _setValid = async (shouldUpdateValid) => {\n        if (!_options.disabled &&\n            (_proxyFormState.isValid ||\n                _proxySubscribeFormState.isValid ||\n                shouldUpdateValid)) {\n            const isValid = _options.resolver\n                ? isEmptyObject((await _runSchema()).errors)\n                : await executeBuiltInValidation(_fields, true);\n            if (isValid !== _formState.isValid) {\n                _subjects.state.next({\n                    isValid,\n                });\n            }\n        }\n    };\n    const _updateIsValidating = (names, isValidating) => {\n        if (!_options.disabled &&\n            (_proxyFormState.isValidating ||\n                _proxyFormState.validatingFields ||\n                _proxySubscribeFormState.isValidating ||\n                _proxySubscribeFormState.validatingFields)) {\n            (names || Array.from(_names.mount)).forEach((name) => {\n                if (name) {\n                    isValidating\n                        ? set(_formState.validatingFields, name, isValidating)\n                        : unset(_formState.validatingFields, name);\n                }\n            });\n            _subjects.state.next({\n                validatingFields: _formState.validatingFields,\n                isValidating: !isEmptyObject(_formState.validatingFields),\n            });\n        }\n    };\n    const _setFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true) => {\n        if (args && method && !_options.disabled) {\n            _state.action = true;\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n                const fieldValues = method(get(_fields, name), args.argA, args.argB);\n                shouldSetValues && set(_fields, name, fieldValues);\n            }\n            if (shouldUpdateFieldsAndState &&\n                Array.isArray(get(_formState.errors, name))) {\n                const errors = method(get(_formState.errors, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.errors, name, errors);\n                unsetEmptyArray(_formState.errors, name);\n            }\n            if ((_proxyFormState.touchedFields ||\n                _proxySubscribeFormState.touchedFields) &&\n                shouldUpdateFieldsAndState &&\n                Array.isArray(get(_formState.touchedFields, name))) {\n                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n            }\n            if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n            }\n            _subjects.state.next({\n                name,\n                isDirty: _getDirty(name, values),\n                dirtyFields: _formState.dirtyFields,\n                errors: _formState.errors,\n                isValid: _formState.isValid,\n            });\n        }\n        else {\n            set(_formValues, name, values);\n        }\n    };\n    const updateErrors = (name, error) => {\n        set(_formState.errors, name, error);\n        _subjects.state.next({\n            errors: _formState.errors,\n        });\n    };\n    const _setErrors = (errors) => {\n        _formState.errors = errors;\n        _subjects.state.next({\n            errors: _formState.errors,\n            isValid: false,\n        });\n    };\n    const updateValidAndValue = (name, shouldSkipSetValueAs, value, ref) => {\n        const field = get(_fields, name);\n        if (field) {\n            const defaultValue = get(_formValues, name, isUndefined(value) ? get(_defaultValues, name) : value);\n            isUndefined(defaultValue) ||\n                (ref && ref.defaultChecked) ||\n                shouldSkipSetValueAs\n                ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f))\n                : setFieldValue(name, defaultValue);\n            _state.mount && _setValid();\n        }\n    };\n    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender) => {\n        let shouldUpdateField = false;\n        let isPreviousDirty = false;\n        const output = {\n            name,\n        };\n        if (!_options.disabled) {\n            if (!isBlurEvent || shouldDirty) {\n                if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n                    isPreviousDirty = _formState.isDirty;\n                    _formState.isDirty = output.isDirty = _getDirty();\n                    shouldUpdateField = isPreviousDirty !== output.isDirty;\n                }\n                const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n                isPreviousDirty = !!get(_formState.dirtyFields, name);\n                isCurrentFieldPristine\n                    ? unset(_formState.dirtyFields, name)\n                    : set(_formState.dirtyFields, name, true);\n                output.dirtyFields = _formState.dirtyFields;\n                shouldUpdateField =\n                    shouldUpdateField ||\n                        ((_proxyFormState.dirtyFields ||\n                            _proxySubscribeFormState.dirtyFields) &&\n                            isPreviousDirty !== !isCurrentFieldPristine);\n            }\n            if (isBlurEvent) {\n                const isPreviousFieldTouched = get(_formState.touchedFields, name);\n                if (!isPreviousFieldTouched) {\n                    set(_formState.touchedFields, name, isBlurEvent);\n                    output.touchedFields = _formState.touchedFields;\n                    shouldUpdateField =\n                        shouldUpdateField ||\n                            ((_proxyFormState.touchedFields ||\n                                _proxySubscribeFormState.touchedFields) &&\n                                isPreviousFieldTouched !== isBlurEvent);\n                }\n            }\n            shouldUpdateField && shouldRender && _subjects.state.next(output);\n        }\n        return shouldUpdateField ? output : {};\n    };\n    const shouldRenderByError = (name, isValid, error, fieldState) => {\n        const previousFieldError = get(_formState.errors, name);\n        const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n            isBoolean(isValid) &&\n            _formState.isValid !== isValid;\n        if (_options.delayError && error) {\n            delayErrorCallback = debounce(() => updateErrors(name, error));\n            delayErrorCallback(_options.delayError);\n        }\n        else {\n            clearTimeout(timer);\n            delayErrorCallback = null;\n            error\n                ? set(_formState.errors, name, error)\n                : unset(_formState.errors, name);\n        }\n        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n            !isEmptyObject(fieldState) ||\n            shouldUpdateValid) {\n            const updatedFormState = {\n                ...fieldState,\n                ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n                errors: _formState.errors,\n                name,\n            };\n            _formState = {\n                ..._formState,\n                ...updatedFormState,\n            };\n            _subjects.state.next(updatedFormState);\n        }\n    };\n    const _runSchema = async (name) => {\n        _updateIsValidating(name, true);\n        const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n        _updateIsValidating(name);\n        return result;\n    };\n    const executeSchemaAndUpdateState = async (names) => {\n        const { errors } = await _runSchema(names);\n        if (names) {\n            for (const name of names) {\n                const error = get(errors, name);\n                error\n                    ? set(_formState.errors, name, error)\n                    : unset(_formState.errors, name);\n            }\n        }\n        else {\n            _formState.errors = errors;\n        }\n        return errors;\n    };\n    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n        valid: true,\n    }) => {\n        for (const name in fields) {\n            const field = fields[name];\n            if (field) {\n                const { _f, ...fieldValue } = field;\n                if (_f) {\n                    const isFieldArrayRoot = _names.array.has(_f.name);\n                    const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([name], true);\n                    }\n                    const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([name]);\n                    }\n                    if (fieldError[_f.name]) {\n                        context.valid = false;\n                        if (shouldOnlyCheckValid) {\n                            break;\n                        }\n                    }\n                    !shouldOnlyCheckValid &&\n                        (get(fieldError, _f.name)\n                            ? isFieldArrayRoot\n                                ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name)\n                                : set(_formState.errors, _f.name, fieldError[_f.name])\n                            : unset(_formState.errors, _f.name));\n                }\n                !isEmptyObject(fieldValue) &&\n                    (await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context));\n            }\n        }\n        return context.valid;\n    };\n    const _removeUnmounted = () => {\n        for (const name of _names.unMount) {\n            const field = get(_fields, name);\n            field &&\n                (field._f.refs\n                    ? field._f.refs.every((ref) => !live(ref))\n                    : !live(field._f.ref)) &&\n                unregister(name);\n        }\n        _names.unMount = new Set();\n    };\n    const _getDirty = (name, data) => !_options.disabled &&\n        (name && data && set(_formValues, name, data),\n            !deepEqual(getValues(), _defaultValues));\n    const _getWatch = (names, defaultValue, isGlobal) => generateWatchOutput(names, _names, {\n        ...(_state.mount\n            ? _formValues\n            : isUndefined(defaultValue)\n                ? _defaultValues\n                : isString(names)\n                    ? { [names]: defaultValue }\n                    : defaultValue),\n    }, isGlobal, defaultValue);\n    const _getFieldArray = (name) => compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n    const setFieldValue = (name, value, options = {}) => {\n        const field = get(_fields, name);\n        let fieldValue = value;\n        if (field) {\n            const fieldReference = field._f;\n            if (fieldReference) {\n                !fieldReference.disabled &&\n                    set(_formValues, name, getFieldValueAs(value, fieldReference));\n                fieldValue =\n                    isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n                        ? ''\n                        : value;\n                if (isMultipleSelect(fieldReference.ref)) {\n                    [...fieldReference.ref.options].forEach((optionRef) => (optionRef.selected = fieldValue.includes(optionRef.value)));\n                }\n                else if (fieldReference.refs) {\n                    if (isCheckBoxInput(fieldReference.ref)) {\n                        fieldReference.refs.forEach((checkboxRef) => {\n                            if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                                if (Array.isArray(fieldValue)) {\n                                    checkboxRef.checked = !!fieldValue.find((data) => data === checkboxRef.value);\n                                }\n                                else {\n                                    checkboxRef.checked =\n                                        fieldValue === checkboxRef.value || !!fieldValue;\n                                }\n                            }\n                        });\n                    }\n                    else {\n                        fieldReference.refs.forEach((radioRef) => (radioRef.checked = radioRef.value === fieldValue));\n                    }\n                }\n                else if (isFileInput(fieldReference.ref)) {\n                    fieldReference.ref.value = '';\n                }\n                else {\n                    fieldReference.ref.value = fieldValue;\n                    if (!fieldReference.ref.type) {\n                        _subjects.state.next({\n                            name,\n                            values: cloneObject(_formValues),\n                        });\n                    }\n                }\n            }\n        }\n        (options.shouldDirty || options.shouldTouch) &&\n            updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n        options.shouldValidate && trigger(name);\n    };\n    const setValues = (name, value, options) => {\n        for (const fieldKey in value) {\n            if (!value.hasOwnProperty(fieldKey)) {\n                return;\n            }\n            const fieldValue = value[fieldKey];\n            const fieldName = name + '.' + fieldKey;\n            const field = get(_fields, fieldName);\n            (_names.array.has(name) ||\n                isObject(fieldValue) ||\n                (field && !field._f)) &&\n                !isDateObject(fieldValue)\n                ? setValues(fieldName, fieldValue, options)\n                : setFieldValue(fieldName, fieldValue, options);\n        }\n    };\n    const setValue = (name, value, options = {}) => {\n        const field = get(_fields, name);\n        const isFieldArray = _names.array.has(name);\n        const cloneValue = cloneObject(value);\n        set(_formValues, name, cloneValue);\n        if (isFieldArray) {\n            _subjects.array.next({\n                name,\n                values: cloneObject(_formValues),\n            });\n            if ((_proxyFormState.isDirty ||\n                _proxyFormState.dirtyFields ||\n                _proxySubscribeFormState.isDirty ||\n                _proxySubscribeFormState.dirtyFields) &&\n                options.shouldDirty) {\n                _subjects.state.next({\n                    name,\n                    dirtyFields: getDirtyFields(_defaultValues, _formValues),\n                    isDirty: _getDirty(name, cloneValue),\n                });\n            }\n        }\n        else {\n            field && !field._f && !isNullOrUndefined(cloneValue)\n                ? setValues(name, cloneValue, options)\n                : setFieldValue(name, cloneValue, options);\n        }\n        isWatched(name, _names) && _subjects.state.next({ ..._formState });\n        _subjects.state.next({\n            name: _state.mount ? name : undefined,\n            values: cloneObject(_formValues),\n        });\n    };\n    const onChange = async (event) => {\n        _state.mount = true;\n        const target = event.target;\n        let name = target.name;\n        let isFieldValueUpdated = true;\n        const field = get(_fields, name);\n        const _updateIsFieldValueUpdated = (fieldValue) => {\n            isFieldValueUpdated =\n                Number.isNaN(fieldValue) ||\n                    (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n                    deepEqual(fieldValue, get(_formValues, name, fieldValue));\n        };\n        const validationModeBeforeSubmit = getValidationModes(_options.mode);\n        const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n        if (field) {\n            let error;\n            let isValid;\n            const fieldValue = target.type\n                ? getFieldValue(field._f)\n                : getEventValue(event);\n            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n            const shouldSkipValidation = (!hasValidation(field._f) &&\n                !_options.resolver &&\n                !get(_formState.errors, name) &&\n                !field._f.deps) ||\n                skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n            const watched = isWatched(name, _names, isBlurEvent);\n            set(_formValues, name, fieldValue);\n            if (isBlurEvent) {\n                field._f.onBlur && field._f.onBlur(event);\n                delayErrorCallback && delayErrorCallback(0);\n            }\n            else if (field._f.onChange) {\n                field._f.onChange(event);\n            }\n            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n            const shouldRender = !isEmptyObject(fieldState) || watched;\n            !isBlurEvent &&\n                _subjects.state.next({\n                    name,\n                    type: event.type,\n                    values: cloneObject(_formValues),\n                });\n            if (shouldSkipValidation) {\n                if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                    if (_options.mode === 'onBlur') {\n                        if (isBlurEvent) {\n                            _setValid();\n                        }\n                    }\n                    else if (!isBlurEvent) {\n                        _setValid();\n                    }\n                }\n                return (shouldRender &&\n                    _subjects.state.next({ name, ...(watched ? {} : fieldState) }));\n            }\n            !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n            if (_options.resolver) {\n                const { errors } = await _runSchema([name]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n                    error = errorLookupResult.error;\n                    name = errorLookupResult.name;\n                    isValid = isEmptyObject(errors);\n                }\n            }\n            else {\n                _updateIsValidating([name], true);\n                error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n                _updateIsValidating([name]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    if (error) {\n                        isValid = false;\n                    }\n                    else if (_proxyFormState.isValid ||\n                        _proxySubscribeFormState.isValid) {\n                        isValid = await executeBuiltInValidation(_fields, true);\n                    }\n                }\n            }\n            if (isFieldValueUpdated) {\n                field._f.deps &&\n                    trigger(field._f.deps);\n                shouldRenderByError(name, isValid, error, fieldState);\n            }\n        }\n    };\n    const _focusInput = (ref, key) => {\n        if (get(_formState.errors, key) && ref.focus) {\n            ref.focus();\n            return 1;\n        }\n        return;\n    };\n    const trigger = async (name, options = {}) => {\n        let isValid;\n        let validationResult;\n        const fieldNames = convertToArrayPayload(name);\n        if (_options.resolver) {\n            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n            isValid = isEmptyObject(errors);\n            validationResult = name\n                ? !fieldNames.some((name) => get(errors, name))\n                : isValid;\n        }\n        else if (name) {\n            validationResult = (await Promise.all(fieldNames.map(async (fieldName) => {\n                const field = get(_fields, fieldName);\n                return await executeBuiltInValidation(field && field._f ? { [fieldName]: field } : field);\n            }))).every(Boolean);\n            !(!validationResult && !_formState.isValid) && _setValid();\n        }\n        else {\n            validationResult = isValid = await executeBuiltInValidation(_fields);\n        }\n        _subjects.state.next({\n            ...(!isString(name) ||\n                ((_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n                    isValid !== _formState.isValid)\n                ? {}\n                : { name }),\n            ...(_options.resolver || !name ? { isValid } : {}),\n            errors: _formState.errors,\n        });\n        options.shouldFocus &&\n            !validationResult &&\n            iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n        return validationResult;\n    };\n    const getValues = (fieldNames) => {\n        const values = {\n            ...(_state.mount ? _formValues : _defaultValues),\n        };\n        return isUndefined(fieldNames)\n            ? values\n            : isString(fieldNames)\n                ? get(values, fieldNames)\n                : fieldNames.map((name) => get(values, name));\n    };\n    const getFieldState = (name, formState) => ({\n        invalid: !!get((formState || _formState).errors, name),\n        isDirty: !!get((formState || _formState).dirtyFields, name),\n        error: get((formState || _formState).errors, name),\n        isValidating: !!get(_formState.validatingFields, name),\n        isTouched: !!get((formState || _formState).touchedFields, name),\n    });\n    const clearErrors = (name) => {\n        name &&\n            convertToArrayPayload(name).forEach((inputName) => unset(_formState.errors, inputName));\n        _subjects.state.next({\n            errors: name ? _formState.errors : {},\n        });\n    };\n    const setError = (name, error, options) => {\n        const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n        const currentError = get(_formState.errors, name) || {};\n        // Don't override existing error messages elsewhere in the object tree.\n        const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n        set(_formState.errors, name, {\n            ...restOfErrorTree,\n            ...error,\n            ref,\n        });\n        _subjects.state.next({\n            name,\n            errors: _formState.errors,\n            isValid: false,\n        });\n        options && options.shouldFocus && ref && ref.focus && ref.focus();\n    };\n    const watch = (name, defaultValue) => isFunction(name)\n        ? _subjects.state.subscribe({\n            next: (payload) => name(_getWatch(undefined, defaultValue), payload),\n        })\n        : _getWatch(name, defaultValue, true);\n    const _subscribe = (props) => _subjects.state.subscribe({\n        next: (formState) => {\n            if (shouldSubscribeByName(props.name, formState.name, props.exact) &&\n                shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {\n                props.callback({\n                    values: { ..._formValues },\n                    ..._formState,\n                    ...formState,\n                });\n            }\n        },\n    }).unsubscribe;\n    const subscribe = (props) => {\n        _state.mount = true;\n        _proxySubscribeFormState = {\n            ..._proxySubscribeFormState,\n            ...props.formState,\n        };\n        return _subscribe({\n            ...props,\n            formState: _proxySubscribeFormState,\n        });\n    };\n    const unregister = (name, options = {}) => {\n        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n            _names.mount.delete(fieldName);\n            _names.array.delete(fieldName);\n            if (!options.keepValue) {\n                unset(_fields, fieldName);\n                unset(_formValues, fieldName);\n            }\n            !options.keepError && unset(_formState.errors, fieldName);\n            !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n            !options.keepTouched && unset(_formState.touchedFields, fieldName);\n            !options.keepIsValidating &&\n                unset(_formState.validatingFields, fieldName);\n            !_options.shouldUnregister &&\n                !options.keepDefaultValue &&\n                unset(_defaultValues, fieldName);\n        }\n        _subjects.state.next({\n            values: cloneObject(_formValues),\n        });\n        _subjects.state.next({\n            ..._formState,\n            ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n        });\n        !options.keepIsValid && _setValid();\n    };\n    const _setDisabledField = ({ disabled, name, }) => {\n        if ((isBoolean(disabled) && _state.mount) ||\n            !!disabled ||\n            _names.disabled.has(name)) {\n            disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n        }\n    };\n    const register = (name, options = {}) => {\n        let field = get(_fields, name);\n        const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n        set(_fields, name, {\n            ...(field || {}),\n            _f: {\n                ...(field && field._f ? field._f : { ref: { name } }),\n                name,\n                mount: true,\n                ...options,\n            },\n        });\n        _names.mount.add(name);\n        if (field) {\n            _setDisabledField({\n                disabled: isBoolean(options.disabled)\n                    ? options.disabled\n                    : _options.disabled,\n                name,\n            });\n        }\n        else {\n            updateValidAndValue(name, true, options.value);\n        }\n        return {\n            ...(disabledIsDefined\n                ? { disabled: options.disabled || _options.disabled }\n                : {}),\n            ...(_options.progressive\n                ? {\n                    required: !!options.required,\n                    min: getRuleValue(options.min),\n                    max: getRuleValue(options.max),\n                    minLength: getRuleValue(options.minLength),\n                    maxLength: getRuleValue(options.maxLength),\n                    pattern: getRuleValue(options.pattern),\n                }\n                : {}),\n            name,\n            onChange,\n            onBlur: onChange,\n            ref: (ref) => {\n                if (ref) {\n                    register(name, options);\n                    field = get(_fields, name);\n                    const fieldRef = isUndefined(ref.value)\n                        ? ref.querySelectorAll\n                            ? ref.querySelectorAll('input,select,textarea')[0] || ref\n                            : ref\n                        : ref;\n                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n                    const refs = field._f.refs || [];\n                    if (radioOrCheckbox\n                        ? refs.find((option) => option === fieldRef)\n                        : fieldRef === field._f.ref) {\n                        return;\n                    }\n                    set(_fields, name, {\n                        _f: {\n                            ...field._f,\n                            ...(radioOrCheckbox\n                                ? {\n                                    refs: [\n                                        ...refs.filter(live),\n                                        fieldRef,\n                                        ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                                    ],\n                                    ref: { type: fieldRef.type, name },\n                                }\n                                : { ref: fieldRef }),\n                        },\n                    });\n                    updateValidAndValue(name, false, undefined, fieldRef);\n                }\n                else {\n                    field = get(_fields, name, {});\n                    if (field._f) {\n                        field._f.mount = false;\n                    }\n                    (_options.shouldUnregister || options.shouldUnregister) &&\n                        !(isNameInFieldArray(_names.array, name) && _state.action) &&\n                        _names.unMount.add(name);\n                }\n            },\n        };\n    };\n    const _focusError = () => _options.shouldFocusError &&\n        iterateFieldsByAction(_fields, _focusInput, _names.mount);\n    const _disableForm = (disabled) => {\n        if (isBoolean(disabled)) {\n            _subjects.state.next({ disabled });\n            iterateFieldsByAction(_fields, (ref, name) => {\n                const currentField = get(_fields, name);\n                if (currentField) {\n                    ref.disabled = currentField._f.disabled || disabled;\n                    if (Array.isArray(currentField._f.refs)) {\n                        currentField._f.refs.forEach((inputRef) => {\n                            inputRef.disabled = currentField._f.disabled || disabled;\n                        });\n                    }\n                }\n            }, 0, false);\n        }\n    };\n    const handleSubmit = (onValid, onInvalid) => async (e) => {\n        let onValidError = undefined;\n        if (e) {\n            e.preventDefault && e.preventDefault();\n            e.persist &&\n                e.persist();\n        }\n        let fieldValues = cloneObject(_formValues);\n        _subjects.state.next({\n            isSubmitting: true,\n        });\n        if (_options.resolver) {\n            const { errors, values } = await _runSchema();\n            _formState.errors = errors;\n            fieldValues = cloneObject(values);\n        }\n        else {\n            await executeBuiltInValidation(_fields);\n        }\n        if (_names.disabled.size) {\n            for (const name of _names.disabled) {\n                unset(fieldValues, name);\n            }\n        }\n        unset(_formState.errors, 'root');\n        if (isEmptyObject(_formState.errors)) {\n            _subjects.state.next({\n                errors: {},\n            });\n            try {\n                await onValid(fieldValues, e);\n            }\n            catch (error) {\n                onValidError = error;\n            }\n        }\n        else {\n            if (onInvalid) {\n                await onInvalid({ ..._formState.errors }, e);\n            }\n            _focusError();\n            setTimeout(_focusError);\n        }\n        _subjects.state.next({\n            isSubmitted: true,\n            isSubmitting: false,\n            isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n            submitCount: _formState.submitCount + 1,\n            errors: _formState.errors,\n        });\n        if (onValidError) {\n            throw onValidError;\n        }\n    };\n    const resetField = (name, options = {}) => {\n        if (get(_fields, name)) {\n            if (isUndefined(options.defaultValue)) {\n                setValue(name, cloneObject(get(_defaultValues, name)));\n            }\n            else {\n                setValue(name, options.defaultValue);\n                set(_defaultValues, name, cloneObject(options.defaultValue));\n            }\n            if (!options.keepTouched) {\n                unset(_formState.touchedFields, name);\n            }\n            if (!options.keepDirty) {\n                unset(_formState.dirtyFields, name);\n                _formState.isDirty = options.defaultValue\n                    ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n                    : _getDirty();\n            }\n            if (!options.keepError) {\n                unset(_formState.errors, name);\n                _proxyFormState.isValid && _setValid();\n            }\n            _subjects.state.next({ ..._formState });\n        }\n    };\n    const _reset = (formValues, keepStateOptions = {}) => {\n        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n        const cloneUpdatedValues = cloneObject(updatedValues);\n        const isEmptyResetValues = isEmptyObject(formValues);\n        const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n        if (!keepStateOptions.keepDefaultValues) {\n            _defaultValues = updatedValues;\n        }\n        if (!keepStateOptions.keepValues) {\n            if (keepStateOptions.keepDirtyValues) {\n                const fieldsToCheck = new Set([\n                    ..._names.mount,\n                    ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n                ]);\n                for (const fieldName of Array.from(fieldsToCheck)) {\n                    get(_formState.dirtyFields, fieldName)\n                        ? set(values, fieldName, get(_formValues, fieldName))\n                        : setValue(fieldName, get(values, fieldName));\n                }\n            }\n            else {\n                if (isWeb && isUndefined(formValues)) {\n                    for (const name of _names.mount) {\n                        const field = get(_fields, name);\n                        if (field && field._f) {\n                            const fieldReference = Array.isArray(field._f.refs)\n                                ? field._f.refs[0]\n                                : field._f.ref;\n                            if (isHTMLElement(fieldReference)) {\n                                const form = fieldReference.closest('form');\n                                if (form) {\n                                    form.reset();\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n                if (keepStateOptions.keepFieldsRef) {\n                    for (const fieldName of _names.mount) {\n                        setValue(fieldName, get(values, fieldName));\n                    }\n                }\n                else {\n                    _fields = {};\n                }\n            }\n            _formValues = _options.shouldUnregister\n                ? keepStateOptions.keepDefaultValues\n                    ? cloneObject(_defaultValues)\n                    : {}\n                : cloneObject(values);\n            _subjects.array.next({\n                values: { ...values },\n            });\n            _subjects.state.next({\n                values: { ...values },\n            });\n        }\n        _names = {\n            mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n            unMount: new Set(),\n            array: new Set(),\n            disabled: new Set(),\n            watch: new Set(),\n            watchAll: false,\n            focus: '',\n        };\n        _state.mount =\n            !_proxyFormState.isValid ||\n                !!keepStateOptions.keepIsValid ||\n                !!keepStateOptions.keepDirtyValues;\n        _state.watch = !!_options.shouldUnregister;\n        _subjects.state.next({\n            submitCount: keepStateOptions.keepSubmitCount\n                ? _formState.submitCount\n                : 0,\n            isDirty: isEmptyResetValues\n                ? false\n                : keepStateOptions.keepDirty\n                    ? _formState.isDirty\n                    : !!(keepStateOptions.keepDefaultValues &&\n                        !deepEqual(formValues, _defaultValues)),\n            isSubmitted: keepStateOptions.keepIsSubmitted\n                ? _formState.isSubmitted\n                : false,\n            dirtyFields: isEmptyResetValues\n                ? {}\n                : keepStateOptions.keepDirtyValues\n                    ? keepStateOptions.keepDefaultValues && _formValues\n                        ? getDirtyFields(_defaultValues, _formValues)\n                        : _formState.dirtyFields\n                    : keepStateOptions.keepDefaultValues && formValues\n                        ? getDirtyFields(_defaultValues, formValues)\n                        : keepStateOptions.keepDirty\n                            ? _formState.dirtyFields\n                            : {},\n            touchedFields: keepStateOptions.keepTouched\n                ? _formState.touchedFields\n                : {},\n            errors: keepStateOptions.keepErrors ? _formState.errors : {},\n            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n                ? _formState.isSubmitSuccessful\n                : false,\n            isSubmitting: false,\n        });\n    };\n    const reset = (formValues, keepStateOptions) => _reset(isFunction(formValues)\n        ? formValues(_formValues)\n        : formValues, keepStateOptions);\n    const setFocus = (name, options = {}) => {\n        const field = get(_fields, name);\n        const fieldReference = field && field._f;\n        if (fieldReference) {\n            const fieldRef = fieldReference.refs\n                ? fieldReference.refs[0]\n                : fieldReference.ref;\n            if (fieldRef.focus) {\n                fieldRef.focus();\n                options.shouldSelect &&\n                    isFunction(fieldRef.select) &&\n                    fieldRef.select();\n            }\n        }\n    };\n    const _setFormState = (updatedFormState) => {\n        _formState = {\n            ..._formState,\n            ...updatedFormState,\n        };\n    };\n    const _resetDefaultValues = () => isFunction(_options.defaultValues) &&\n        _options.defaultValues().then((values) => {\n            reset(values, _options.resetOptions);\n            _subjects.state.next({\n                isLoading: false,\n            });\n        });\n    const methods = {\n        control: {\n            register,\n            unregister,\n            getFieldState,\n            handleSubmit,\n            setError,\n            _subscribe,\n            _runSchema,\n            _focusError,\n            _getWatch,\n            _getDirty,\n            _setValid,\n            _setFieldArray,\n            _setDisabledField,\n            _setErrors,\n            _getFieldArray,\n            _reset,\n            _resetDefaultValues,\n            _removeUnmounted,\n            _disableForm,\n            _subjects,\n            _proxyFormState,\n            get _fields() {\n                return _fields;\n            },\n            get _formValues() {\n                return _formValues;\n            },\n            get _state() {\n                return _state;\n            },\n            set _state(value) {\n                _state = value;\n            },\n            get _defaultValues() {\n                return _defaultValues;\n            },\n            get _names() {\n                return _names;\n            },\n            set _names(value) {\n                _names = value;\n            },\n            get _formState() {\n                return _formState;\n            },\n            get _options() {\n                return _options;\n            },\n            set _options(value) {\n                _options = {\n                    ..._options,\n                    ...value,\n                };\n            },\n        },\n        subscribe,\n        trigger,\n        register,\n        handleSubmit,\n        watch,\n        setValue,\n        getValues,\n        reset,\n        resetField,\n        clearErrors,\n        unregister,\n        setError,\n        setFocus,\n        getFieldState,\n    };\n    return {\n        ...methods,\n        formControl: methods,\n    };\n}\n\nvar generateId = () => {\n    if (typeof crypto !== 'undefined' && crypto.randomUUID) {\n        return crypto.randomUUID();\n    }\n    const d = typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n        const r = (Math.random() * 16 + d) % 16 | 0;\n        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n    });\n};\n\nvar getFocusFieldName = (name, index, options = {}) => options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n        `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n\nvar appendAt = (data, value) => [\n    ...data,\n    ...convertToArrayPayload(value),\n];\n\nvar fillEmptyArray = (value) => Array.isArray(value) ? value.map(() => undefined) : undefined;\n\nfunction insert(data, index, value) {\n    return [\n        ...data.slice(0, index),\n        ...convertToArrayPayload(value),\n        ...data.slice(index),\n    ];\n}\n\nvar moveArrayAt = (data, from, to) => {\n    if (!Array.isArray(data)) {\n        return [];\n    }\n    if (isUndefined(data[to])) {\n        data[to] = undefined;\n    }\n    data.splice(to, 0, data.splice(from, 1)[0]);\n    return data;\n};\n\nvar prependAt = (data, value) => [\n    ...convertToArrayPayload(value),\n    ...convertToArrayPayload(data),\n];\n\nfunction removeAtIndexes(data, indexes) {\n    let i = 0;\n    const temp = [...data];\n    for (const index of indexes) {\n        temp.splice(index - i, 1);\n        i++;\n    }\n    return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index) => isUndefined(index)\n    ? []\n    : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b) => a - b));\n\nvar swapArrayAt = (data, indexA, indexB) => {\n    [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n\nvar updateAt = (fieldValues, index, value) => {\n    fieldValues[index] = value;\n    return fieldValues;\n};\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFieldArray(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, keyName = 'id', shouldUnregister, rules, } = props;\n    const [fields, setFields] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getFieldArray(name));\n    const ids = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control._getFieldArray(name).map(generateId));\n    const _fieldIds = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fields);\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    const _actioned = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    _name.current = name;\n    _fieldIds.current = fields;\n    control._names.array.add(name);\n    rules &&\n        control.register(name, rules);\n    useIsomorphicLayoutEffect(() => control._subjects.array.subscribe({\n        next: ({ values, name: fieldArrayName, }) => {\n            if (fieldArrayName === _name.current || !fieldArrayName) {\n                const fieldValues = get(values, _name.current);\n                if (Array.isArray(fieldValues)) {\n                    setFields(fieldValues);\n                    ids.current = fieldValues.map(generateId);\n                }\n            }\n        },\n    }).unsubscribe, [control]);\n    const updateValues = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((updatedFieldArrayValues) => {\n        _actioned.current = true;\n        control._setFieldArray(name, updatedFieldArrayValues);\n    }, [control, name]);\n    const append = (value, options) => {\n        const appendValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n        ids.current = appendAt(ids.current, appendValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n            argA: fillEmptyArray(value),\n        });\n    };\n    const prepend = (value, options) => {\n        const prependValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n        control._names.focus = getFocusFieldName(name, 0, options);\n        ids.current = prependAt(ids.current, prependValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n            argA: fillEmptyArray(value),\n        });\n    };\n    const remove = (index) => {\n        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n        ids.current = removeArrayAt(ids.current, index);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        !Array.isArray(get(control._fields, name)) &&\n            set(control._fields, name, undefined);\n        control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n            argA: index,\n        });\n    };\n    const insert$1 = (index, value, options) => {\n        const insertValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n        control._names.focus = getFocusFieldName(name, index, options);\n        ids.current = insert(ids.current, index, insertValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, insert, {\n            argA: index,\n            argB: fillEmptyArray(value),\n        });\n    };\n    const swap = (indexA, indexB) => {\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n        swapArrayAt(ids.current, indexA, indexB);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n            argA: indexA,\n            argB: indexB,\n        }, false);\n    };\n    const move = (from, to) => {\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        moveArrayAt(updatedFieldArrayValues, from, to);\n        moveArrayAt(ids.current, from, to);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n            argA: from,\n            argB: to,\n        }, false);\n    };\n    const update = (index, value) => {\n        const updateValue = cloneObject(value);\n        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n        ids.current = [...updatedFieldArrayValues].map((item, i) => !item || i === index ? generateId() : ids.current[i]);\n        updateValues(updatedFieldArrayValues);\n        setFields([...updatedFieldArrayValues]);\n        control._setFieldArray(name, updatedFieldArrayValues, updateAt, {\n            argA: index,\n            argB: updateValue,\n        }, true, false);\n    };\n    const replace = (value) => {\n        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n        ids.current = updatedFieldArrayValues.map(generateId);\n        updateValues([...updatedFieldArrayValues]);\n        setFields([...updatedFieldArrayValues]);\n        control._setFieldArray(name, [...updatedFieldArrayValues], (data) => data, {}, true, false);\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        control._state.action = false;\n        isWatched(name, control._names) &&\n            control._subjects.state.next({\n                ...control._formState,\n            });\n        if (_actioned.current &&\n            (!getValidationModes(control._options.mode).isOnSubmit ||\n                control._formState.isSubmitted) &&\n            !getValidationModes(control._options.reValidateMode).isOnSubmit) {\n            if (control._options.resolver) {\n                control._runSchema([name]).then((result) => {\n                    const error = get(result.errors, name);\n                    const existingError = get(control._formState.errors, name);\n                    if (existingError\n                        ? (!error && existingError.type) ||\n                            (error &&\n                                (existingError.type !== error.type ||\n                                    existingError.message !== error.message))\n                        : error && error.type) {\n                        error\n                            ? set(control._formState.errors, name, error)\n                            : unset(control._formState.errors, name);\n                        control._subjects.state.next({\n                            errors: control._formState.errors,\n                        });\n                    }\n                });\n            }\n            else {\n                const field = get(control._fields, name);\n                if (field &&\n                    field._f &&\n                    !(getValidationModes(control._options.reValidateMode).isOnSubmit &&\n                        getValidationModes(control._options.mode).isOnSubmit)) {\n                    validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error) => !isEmptyObject(error) &&\n                        control._subjects.state.next({\n                            errors: updateFieldArrayRootError(control._formState.errors, error, name),\n                        }));\n                }\n            }\n        }\n        control._subjects.state.next({\n            name,\n            values: cloneObject(control._formValues),\n        });\n        control._names.focus &&\n            iterateFieldsByAction(control._fields, (ref, key) => {\n                if (control._names.focus &&\n                    key.startsWith(control._names.focus) &&\n                    ref.focus) {\n                    ref.focus();\n                    return 1;\n                }\n                return;\n            });\n        control._names.focus = '';\n        control._setValid();\n        _actioned.current = false;\n    }, [fields, name, control]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        !get(control._formValues, name) && control._setFieldArray(name);\n        return () => {\n            const updateMounted = (name, value) => {\n                const field = get(control._fields, name);\n                if (field && field._f) {\n                    field._f.mount = value;\n                }\n            };\n            control._options.shouldUnregister || shouldUnregister\n                ? control.unregister(name)\n                : updateMounted(name, false);\n        };\n    }, [name, control, keyName, shouldUnregister]);\n    return {\n        swap: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(swap, [updateValues, name, control]),\n        move: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(move, [updateValues, name, control]),\n        prepend: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(prepend, [updateValues, name, control]),\n        append: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(append, [updateValues, name, control]),\n        remove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(remove, [updateValues, name, control]),\n        insert: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(insert$1, [updateValues, name, control]),\n        update: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(update, [updateValues, name, control]),\n        replace: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(replace, [updateValues, name, control]),\n        fields: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => fields.map((field, index) => ({\n            ...field,\n            [keyName]: ids.current[index] || generateId(),\n        })), [fields, keyName]),\n    };\n}\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useForm(props = {}) {\n    const _formControl = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const _values = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        isDirty: false,\n        isValidating: false,\n        isLoading: isFunction(props.defaultValues),\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        submitCount: 0,\n        dirtyFields: {},\n        touchedFields: {},\n        validatingFields: {},\n        errors: props.errors || {},\n        disabled: props.disabled || false,\n        isReady: false,\n        defaultValues: isFunction(props.defaultValues)\n            ? undefined\n            : props.defaultValues,\n    });\n    if (!_formControl.current) {\n        if (props.formControl) {\n            _formControl.current = {\n                ...props.formControl,\n                formState,\n            };\n            if (props.defaultValues && !isFunction(props.defaultValues)) {\n                props.formControl.reset(props.defaultValues, props.resetOptions);\n            }\n        }\n        else {\n            const { formControl, ...rest } = createFormControl(props);\n            _formControl.current = {\n                ...rest,\n                formState,\n            };\n        }\n    }\n    const control = _formControl.current.control;\n    control._options = props;\n    useIsomorphicLayoutEffect(() => {\n        const sub = control._subscribe({\n            formState: control._proxyFormState,\n            callback: () => updateFormState({ ...control._formState }),\n            reRenderRoot: true,\n        });\n        updateFormState((data) => ({\n            ...data,\n            isReady: true,\n        }));\n        control._formState.isReady = true;\n        return sub;\n    }, [control]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => control._disableForm(props.disabled), [control, props.disabled]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (props.mode) {\n            control._options.mode = props.mode;\n        }\n        if (props.reValidateMode) {\n            control._options.reValidateMode = props.reValidateMode;\n        }\n    }, [control, props.mode, props.reValidateMode]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (props.errors) {\n            control._setErrors(props.errors);\n            control._focusError();\n        }\n    }, [control, props.errors]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        props.shouldUnregister &&\n            control._subjects.state.next({\n                values: control._getWatch(),\n            });\n    }, [control, props.shouldUnregister]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (control._proxyFormState.isDirty) {\n            const isDirty = control._getDirty();\n            if (isDirty !== formState.isDirty) {\n                control._subjects.state.next({\n                    isDirty,\n                });\n            }\n        }\n    }, [control, formState.isDirty]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (props.values && !deepEqual(props.values, _values.current)) {\n            control._reset(props.values, {\n                keepFieldsRef: true,\n                ...control._options.resetOptions,\n            });\n            _values.current = props.values;\n            updateFormState((state) => ({ ...state }));\n        }\n        else {\n            control._resetDefaultValues();\n        }\n    }, [control, props.values]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (!control._state.mount) {\n            control._setValid();\n            control._state.mount = true;\n        }\n        if (control._state.watch) {\n            control._state.watch = false;\n            control._subjects.state.next({ ...control._formState });\n        }\n        control._removeUnmounted();\n    });\n    _formControl.current.formState = getProxyFormState(formState, control);\n    return _formControl.current;\n}\n\n\n//# sourceMappingURL=index.esm.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\n");

/***/ })

};
;