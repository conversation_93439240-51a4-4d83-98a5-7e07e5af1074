'use client';

import React, { useState } from 'react';
import { Clock, MapPin, Phone, User, Smartphone } from 'lucide-react';
import { useServiceRequests, useUpdateServiceRequest } from '@/hooks/useApi';
import { useAuth, withAuth } from '@/contexts/AuthContext';
import { ServiceRequest, ServiceRequestStatus } from '@/types';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { getStatusColor, formatDateTime } from '@/lib/utils';

function TechDashboardPage() {
  const { user } = useAuth();
  const [selectedStatus, setSelectedStatus] = useState<ServiceRequestStatus | 'all'>('all');
  
  const { data: requestsData, isLoading } = useServiceRequests({
    technicianId: user?.id,
    status: selectedStatus === 'all' ? undefined : selectedStatus,
  });

  const updateRequestMutation = useUpdateServiceRequest();

  const handleStatusUpdate = async (requestId: string, newStatus: ServiceRequestStatus) => {
    try {
      await updateRequestMutation.mutateAsync({
        id: requestId,
        data: { status: newStatus },
      });
    } catch (error) {
      console.error('Failed to update status:', error);
    }
  };

  const getStatusActions = (request: ServiceRequest) => {
    const actions: { label: string; status: ServiceRequestStatus; variant: 'primary' | 'secondary' | 'outline' }[] = [];

    switch (request.status) {
      case 'Assigned':
        actions.push({ label: 'On The Way', status: 'OnTheWay', variant: 'primary' });
        break;
      case 'OnTheWay':
        actions.push({ label: 'Start Repair', status: 'InRepair', variant: 'primary' });
        break;
      case 'InRepair':
        actions.push({ label: 'Complete', status: 'Completed', variant: 'primary' });
        break;
    }

    return actions;
  };

  const statusFilters = [
    { label: 'All', value: 'all' as const },
    { label: 'Assigned', value: 'Assigned' as const },
    { label: 'On The Way', value: 'OnTheWay' as const },
    { label: 'In Repair', value: 'InRepair' as const },
    { label: 'Completed', value: 'Completed' as const },
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <LoadingSpinner size="lg" text="Loading your assignments..." />
      </div>
    );
  }

  const requests = requestsData?.data || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Welcome, {user?.name}!
        </h1>
        <p className="text-gray-600">
          You have {requests.length} service requests assigned to you.
        </p>
      </div>

      {/* Status Filter */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex flex-wrap gap-2">
          {statusFilters.map((filter) => (
            <Button
              key={filter.value}
              variant={selectedStatus === filter.value ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setSelectedStatus(filter.value)}
            >
              {filter.label}
            </Button>
          ))}
        </div>
      </div>

      {/* Service Requests */}
      <div className="space-y-4">
        {requests.length === 0 ? (
          <div className="bg-white rounded-lg shadow p-8 text-center">
            <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No requests found</h3>
            <p className="text-gray-600">
              {selectedStatus === 'all' 
                ? "You don't have any service requests assigned yet."
                : `No requests with status "${selectedStatus}".`
              }
            </p>
          </div>
        ) : (
          requests.map((request) => (
            <div key={request.id} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-1">
                    {request.title}
                  </h3>
                  <p className="text-sm text-gray-600 mb-2">
                    Request #{request.id.slice(-8)}
                  </p>
                  <Badge className={getStatusColor(request.status)}>
                    {request.status}
                  </Badge>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500">
                    Created: {formatDateTime(request.createdAt)}
                  </p>
                  {request.scheduledDate && (
                    <p className="text-sm text-gray-500">
                      Scheduled: {formatDateTime(request.scheduledDate)}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                {/* Customer Info */}
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900 flex items-center">
                    <User className="h-4 w-4 mr-2" />
                    Customer
                  </h4>
                  <p className="text-sm text-gray-600">{request.customer?.name}</p>
                  <p className="text-sm text-gray-600 flex items-center">
                    <Phone className="h-4 w-4 mr-1" />
                    {request.customer?.phone}
                  </p>
                  {request.customer?.address && (
                    <p className="text-sm text-gray-600 flex items-center">
                      <MapPin className="h-4 w-4 mr-1" />
                      {request.customer.address}
                    </p>
                  )}
                </div>

                {/* Device Info */}
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900 flex items-center">
                    <Smartphone className="h-4 w-4 mr-2" />
                    Device
                  </h4>
                  <p className="text-sm text-gray-600">
                    {request.device?.brand} {request.device?.model}
                  </p>
                  {request.device?.serialNumber && (
                    <p className="text-sm text-gray-600">
                      S/N: {request.device.serialNumber}
                    </p>
                  )}
                </div>
              </div>

              {/* Description */}
              <div className="mb-4">
                <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                <p className="text-sm text-gray-600">{request.description}</p>
              </div>

              {/* Actions */}
              <div className="flex flex-wrap gap-2">
                {getStatusActions(request).map((action) => (
                  <Button
                    key={action.status}
                    variant={action.variant}
                    size="sm"
                    onClick={() => handleStatusUpdate(request.id, action.status)}
                    isLoading={updateRequestMutation.isPending}
                  >
                    {action.label}
                  </Button>
                ))}
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.location.href = `/requests/${request.id}`}
                >
                  View Details
                </Button>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}

export default withAuth(TechDashboardPage, ['technician']);
