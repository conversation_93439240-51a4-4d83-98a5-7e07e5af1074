import type {
  User,
  AuthResponse,
  LoginCredentials,
  Customer,
  CreateCustomerData,
  Device,
  CreateDeviceData,
  ServiceRequest,
  CreateServiceRequestData,
  UpdateServiceRequestData,
  ServiceNote,
  CreateServiceNoteData,
  Invoice,
  CreateInvoiceData,
  StockItem,
  CreateStockItemData,
  DashboardStats,
  PaginatedResponse,
} from '@/types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api';

class ApiClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private getToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('auth-token');
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = this.getToken();

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(`${this.baseURL}${endpoint}`, config);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    // API response'u data altında dönüyor, onu extract edelim
    if (result.data !== undefined) {
      return result.data;
    }

    return result;
  }

  // Authentication
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    return this.request<AuthResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  async logout(): Promise<void> {
    return this.request<void>('/auth/logout', {
      method: 'POST',
    });
  }

  async getProfile(): Promise<User> {
    return this.request<User>('/auth/profile');
  }

  async updateProfile(data: Partial<User>): Promise<User> {
    return this.request<User>('/auth/profile', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // Dashboard
  async getDashboardStats(): Promise<DashboardStats> {
    return this.request<DashboardStats>('/dashboard/stats');
  }

  // Customers
  async getCustomers(params?: {
    page?: number;
    limit?: number;
    search?: string;
  }): Promise<PaginatedResponse<Customer>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);

    const response = await fetch(`${this.baseURL}/customers?${searchParams.toString()}`, {
      headers: {
        'Content-Type': 'application/json',
        ...(this.getToken() && { Authorization: `Bearer ${this.getToken()}` }),
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    // Pagination response'u data altında dönüyor
    if (result.data) {
      return result.data;
    }

    return result;
  }

  async getCustomer(id: string): Promise<Customer> {
    return this.request<Customer>(`/customers/${id}`);
  }

  async createCustomer(data: CreateCustomerData): Promise<Customer> {
    return this.request<Customer>('/customers', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateCustomer(id: string, data: Partial<CreateCustomerData>): Promise<Customer> {
    return this.request<Customer>(`/customers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteCustomer(id: string): Promise<void> {
    return this.request<void>(`/customers/${id}`, {
      method: 'DELETE',
    });
  }

  // Devices
  async getDevices(params?: {
    page?: number;
    limit?: number;
    customerId?: string;
  }): Promise<PaginatedResponse<Device>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.customerId) searchParams.append('customerId', params.customerId);

    const response = await fetch(`${this.baseURL}/devices?${searchParams.toString()}`, {
      headers: {
        'Content-Type': 'application/json',
        ...(this.getToken() && { Authorization: `Bearer ${this.getToken()}` }),
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    if (result.data) {
      return result.data;
    }

    return result;
  }

  async getDevice(id: string): Promise<Device> {
    return this.request<Device>(`/devices/${id}`);
  }

  async createDevice(data: CreateDeviceData): Promise<Device> {
    return this.request<Device>('/devices', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateDevice(id: string, data: Partial<CreateDeviceData>): Promise<Device> {
    return this.request<Device>(`/devices/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteDevice(id: string): Promise<void> {
    return this.request<void>(`/devices/${id}`, {
      method: 'DELETE',
    });
  }

  // Service Requests
  async getServiceRequests(params?: {
    page?: number;
    limit?: number;
    status?: string;
    technicianId?: string;
    customerId?: string;
  }): Promise<PaginatedResponse<ServiceRequest>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.status) searchParams.append('status', params.status);
    if (params?.technicianId) searchParams.append('technicianId', params.technicianId);
    if (params?.customerId) searchParams.append('customerId', params.customerId);

    const response = await fetch(`${this.baseURL}/service-requests?${searchParams.toString()}`, {
      headers: {
        'Content-Type': 'application/json',
        ...(this.getToken() && { Authorization: `Bearer ${this.getToken()}` }),
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    if (result.data) {
      return result.data;
    }

    return result;
  }

  async getServiceRequest(id: string): Promise<ServiceRequest> {
    return this.request<ServiceRequest>(`/service-requests/${id}`);
  }

  async createServiceRequest(data: CreateServiceRequestData): Promise<ServiceRequest> {
    return this.request<ServiceRequest>('/service-requests', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateServiceRequest(
    id: string,
    data: UpdateServiceRequestData
  ): Promise<ServiceRequest> {
    return this.request<ServiceRequest>(`/service-requests/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteServiceRequest(id: string): Promise<void> {
    return this.request<void>(`/service-requests/${id}`, {
      method: 'DELETE',
    });
  }

  // Service Notes
  async getServiceNotes(serviceRequestId: string): Promise<ServiceNote[]> {
    return this.request<ServiceNote[]>(`/service-requests/${serviceRequestId}/notes`);
  }

  async createServiceNote(
    serviceRequestId: string,
    data: CreateServiceNoteData
  ): Promise<ServiceNote> {
    const formData = new FormData();
    formData.append('note', data.note);
    if (data.image) {
      formData.append('image', data.image);
    }

    const token = this.getToken();
    const response = await fetch(
      `${this.baseURL}/service-requests/${serviceRequestId}/notes`,
      {
        method: 'POST',
        headers: {
          ...(token && { Authorization: `Bearer ${token}` }),
        },
        body: formData,
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  // Users/Technicians
  async getTechnicians(): Promise<User[]> {
    return this.request<User[]>('/users?role=technician');
  }

  async getUsers(role?: string): Promise<User[]> {
    const searchParams = new URLSearchParams();
    if (role) searchParams.append('role', role);
    
    return this.request<User[]>(`/users?${searchParams.toString()}`);
  }

  // Invoices
  async getInvoices(params?: {
    page?: number;
    limit?: number;
    isPaid?: boolean;
  }): Promise<PaginatedResponse<Invoice>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.isPaid !== undefined) searchParams.append('isPaid', params.isPaid.toString());

    const response = await fetch(`${this.baseURL}/invoices?${searchParams.toString()}`, {
      headers: {
        'Content-Type': 'application/json',
        ...(this.getToken() && { Authorization: `Bearer ${this.getToken()}` }),
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    if (result.data) {
      return result.data;
    }

    return result;
  }

  async getInvoice(id: string): Promise<Invoice> {
    return this.request<Invoice>(`/invoices/${id}`);
  }

  async createInvoice(data: CreateInvoiceData): Promise<Invoice> {
    return this.request<Invoice>('/invoices', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateInvoice(id: string, data: Partial<CreateInvoiceData>): Promise<Invoice> {
    return this.request<Invoice>(`/invoices/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async markInvoicePaid(id: string): Promise<Invoice> {
    return this.request<Invoice>(`/invoices/${id}/pay`, {
      method: 'POST',
    });
  }

  async downloadInvoicePDF(id: string): Promise<Blob> {
    const token = this.getToken();
    const response = await fetch(`${this.baseURL}/invoices/${id}/pdf`, {
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.blob();
  }

  // Stock
  async getStockItems(params?: {
    page?: number;
    limit?: number;
    search?: string;
    lowStock?: boolean;
  }): Promise<PaginatedResponse<StockItem>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);
    if (params?.lowStock) searchParams.append('lowStock', 'true');

    const response = await fetch(`${this.baseURL}/stock?${searchParams.toString()}`, {
      headers: {
        'Content-Type': 'application/json',
        ...(this.getToken() && { Authorization: `Bearer ${this.getToken()}` }),
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    if (result.data) {
      return result.data;
    }

    return result;
  }

  async getStockItem(id: string): Promise<StockItem> {
    return this.request<StockItem>(`/stock/${id}`);
  }

  async createStockItem(data: CreateStockItemData): Promise<StockItem> {
    return this.request<StockItem>('/stock', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateStockItem(id: string, data: Partial<CreateStockItemData>): Promise<StockItem> {
    return this.request<StockItem>(`/stock/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteStockItem(id: string): Promise<void> {
    return this.request<void>(`/stock/${id}`, {
      method: 'DELETE',
    });
  }
}

export const apiClient = new ApiClient(API_BASE_URL);
export default apiClient;
