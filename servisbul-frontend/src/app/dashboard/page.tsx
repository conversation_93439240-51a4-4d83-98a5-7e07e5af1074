'use client';

import React from 'react';
import { Users, ClipboardList, Wrench, DollarSign, Package, AlertTriangle } from 'lucide-react';
import { useDashboardStats } from '@/hooks/useApi';
import { useAuth } from '@/contexts/AuthContext';
import { withAuth } from '@/contexts/AuthContext';
import StatsCard from '@/components/dashboard/StatsCard';
import { formatCurrency } from '@/lib/utils';

function DashboardPage() {
  const { user } = useAuth();
  const { data: stats, isLoading } = useDashboardStats();

  const statsCards = [
    {
      title: 'Total Customers',
      value: stats?.totalCustomers || 0,
      icon: Users,
      color: 'blue' as const,
    },
    {
      title: 'Service Requests',
      value: stats?.totalRequests || 0,
      icon: ClipboardList,
      color: 'green' as const,
    },
    {
      title: 'Active Technicians',
      value: stats?.assignedTechnicians || 0,
      icon: Wrench,
      color: 'purple' as const,
    },
    {
      title: 'Total Revenue',
      value: stats ? formatCurrency(stats.totalRevenue) : '$0',
      icon: DollarSign,
      color: 'green' as const,
    },
    {
      title: 'Pending Requests',
      value: stats?.pendingRequests || 0,
      icon: AlertTriangle,
      color: 'yellow' as const,
    },
    {
      title: 'Low Stock Items',
      value: stats?.lowStockItems || 0,
      icon: Package,
      color: 'red' as const,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Welcome back, {user?.name}!
        </h1>
        <p className="text-gray-600">
          Here's what's happening with your service management today.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statsCards.map((card, index) => (
          <StatsCard
            key={index}
            title={card.title}
            value={card.value}
            icon={card.icon}
            color={card.color}
            isLoading={isLoading}
          />
        ))}
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <a
            href="/customers"
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Users className="h-8 w-8 text-blue-600 mb-2" />
            <h3 className="font-medium text-gray-900">Add Customer</h3>
            <p className="text-sm text-gray-600">Create a new customer record</p>
          </a>
          
          <a
            href="/requests"
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <ClipboardList className="h-8 w-8 text-green-600 mb-2" />
            <h3 className="font-medium text-gray-900">New Request</h3>
            <p className="text-sm text-gray-600">Create a service request</p>
          </a>
          
          <a
            href="/stock"
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Package className="h-8 w-8 text-purple-600 mb-2" />
            <h3 className="font-medium text-gray-900">Manage Stock</h3>
            <p className="text-sm text-gray-600">Update inventory levels</p>
          </a>
          
          <a
            href="/invoices"
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <DollarSign className="h-8 w-8 text-yellow-600 mb-2" />
            <h3 className="font-medium text-gray-900">Generate Invoice</h3>
            <p className="text-sm text-gray-600">Create new invoice</p>
          </a>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h2>
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <p className="text-sm text-gray-600">
              Service request #SR-001 was completed by John Doe
            </p>
            <span className="text-xs text-gray-400">2 hours ago</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <p className="text-sm text-gray-600">
              New customer "ABC Electronics" was added
            </p>
            <span className="text-xs text-gray-400">4 hours ago</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
            <p className="text-sm text-gray-600">
              Stock alert: iPhone Screen Protector is running low
            </p>
            <span className="text-xs text-gray-400">6 hours ago</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
            <p className="text-sm text-gray-600">
              Invoice #INV-123 was paid by customer
            </p>
            <span className="text-xs text-gray-400">1 day ago</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default withAuth(DashboardPage, ['admin', 'operator']);
