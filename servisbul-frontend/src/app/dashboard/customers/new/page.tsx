'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { useCreateCustomer } from '@/hooks/useApi';
import { withAuth } from '@/contexts/AuthContext';
import { CreateCustomerData } from '@/types';
import Button from '@/components/ui/Button';
import CustomerForm from '@/components/forms/CustomerForm';

function NewCustomerPage() {
  const router = useRouter();
  const createCustomerMutation = useCreateCustomer();

  const handleCreateCustomer = async (data: CreateCustomerData) => {
    try {
      const newCustomer = await createCustomerMutation.mutateAsync(data);
      router.push(`/dashboard/customers/${newCustomer.id}`);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleCancel = () => {
    router.push('/dashboard/customers');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push('/dashboard/customers')}
          leftIcon={<ArrowLeft className="h-4 w-4" />}
        >
          Back to Customers
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Add New Customer</h1>
          <p className="text-gray-600">Create a new customer record</p>
        </div>
      </div>

      {/* Form */}
      <div className="bg-white rounded-lg shadow p-6">
        <CustomerForm
          onSubmit={handleCreateCustomer}
          onCancel={handleCancel}
          isLoading={createCustomerMutation.isPending}
        />
      </div>
    </div>
  );
}

export default withAuth(NewCustomerPage, ['admin']);
