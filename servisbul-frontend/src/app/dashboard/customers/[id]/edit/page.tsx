'use client';

import React from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { useCustomer, useUpdateCustomer } from '@/hooks/useApi';
import { withAuth } from '@/contexts/AuthContext';
import { CreateCustomerData } from '@/types';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import CustomerForm from '@/components/forms/CustomerForm';

function EditCustomerPage() {
  const params = useParams();
  const router = useRouter();
  const customerId = params.id as string;

  const { data: customer, isLoading } = useCustomer(customerId);
  const updateCustomerMutation = useUpdateCustomer();

  const handleUpdateCustomer = async (data: CreateCustomerData) => {
    try {
      await updateCustomerMutation.mutateAsync({
        id: customerId,
        data,
      });
      router.push(`/dashboard/customers/${customerId}`);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleCancel = () => {
    router.push(`/dashboard/customers/${customerId}`);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <LoadingSpinner size="lg" text="Loading customer..." />
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Customer Not Found</h2>
        <p className="text-gray-600 mb-6">The customer you're trying to edit doesn't exist.</p>
        <Button onClick={() => router.push('/dashboard/customers')}>
          Back to Customers
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push(`/dashboard/customers/${customerId}`)}
          leftIcon={<ArrowLeft className="h-4 w-4" />}
        >
          Back to Customer
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Edit Customer</h1>
          <p className="text-gray-600">Update customer information</p>
        </div>
      </div>

      {/* Form */}
      <div className="bg-white rounded-lg shadow p-6">
        <CustomerForm
          customer={customer}
          onSubmit={handleUpdateCustomer}
          onCancel={handleCancel}
          isLoading={updateCustomerMutation.isPending}
        />
      </div>
    </div>
  );
}

export default withAuth(EditCustomerPage, ['admin']);
